# frozen_string_literal: true

require 'yaml'

require_relative '../../models/admin_note'
require_relative '../../models/admin_user'
require_relative '../../models/tasker'
require_relative '../../services/tasker'

module Services
  module PhotoIdentity
    # Service responsible for suspending taskers due to biometric verification issues
    class BiometricSuspension
      SUSPENSION_MESSAGE = 'PrSuspBiometria001 - Profissional suspenso para atualização do cadastro e ' \
                           'biometria facial. A profissional precisa enviar 1. nova foto de perfil; ' \
                           '2. nova foto do documento com CPF; 3. refazer a selfie pelo aplicativo. ' \
                           'Atendimento deve encaminhar os dados em ' \
                           "'#cs-atualização-cadastro-biometria'"

      attr_reader :tasker

      def initialize(tasker)
        @tasker = tasker
      end

      def suspend_with_note
        result = TaskerService.new(tasker).suspend
        raise 'Failed to suspend tasker' unless result

        create_admin_note
      end

      private

      def create_admin_note
        AdminNote.create!(
          note: SUSPENSION_MESSAGE,
          resource_id: tasker.id.to_s,
          resource_class: 'Tasker',
          admin_user: admin_user
        )
      end

      def admin_user
        AdminUser.find_by(email: TaskerService::ADMIN_USER_EMAIL)
      end
    end
  end
end
