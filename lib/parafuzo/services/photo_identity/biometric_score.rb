# frozen_string_literal: true

require 'time'

require_relative '../../../../config/initializers/time_zone'
require_relative '../../models/tasker'
require_relative 'biometric_suspension'

module Services
  module PhotoIdentity
    # Service responsible for processing photo identity scores from Clear Sale
    class BiometricScore
      SUSPENSION_THRESHOLD = 0.7

      attr_reader :transaction_id, :score, :received_at

      delegate :history, to: :tasker, allow_nil: true

      def initialize(transaction_id, score, received_at)
        @transaction_id = transaction_id
        @score = score
        @received_at = received_at
      end

      def process
        return true if invalid_data?

        return true unless tasker

        update_score
      end

      private

      def invalid_data?
        transaction_id.nil? || received_at.nil?
      end

      def tasker
        @tasker ||= Tasker.where('history.photo_identity_transaction_id' => transaction_id).first
      end

      def update_score
        new_received_at = parse_received_at
        return true unless new_received_at

        current_received_at = history.photo_identity_score_received_at

        return unless current_received_at.nil? || new_received_at > current_received_at

        history.photo_identity_score = score
        history.photo_identity_score_received_at = new_received_at
        tasker.save!

        handle_suspension
      end

      def handle_suspension
        return unless suspend?

        BiometricSuspension.new(tasker).suspend_with_note
      end

      def suspend?
        !tasker.suspended? && received_at && score.to_f <= SUSPENSION_THRESHOLD
      end

      def parse_received_at
        Time.iso8601(received_at).utc
      rescue ArgumentError
        nil
      end
    end
  end
end
