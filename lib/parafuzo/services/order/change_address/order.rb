# frozen_string_literal: true

class OrderService::ChangeAddress::Order
  attr_accessor :order, :address, :order_params

  def initialize(order, params, address)
    @order = order
    @address = address
    @order_params = { parameters: order.params_hash.merge(params), service: order.service }
  end

  def run
    order.address.assign_attributes address.compact
    OrderFactory.new(order_params, order).update save: false
    order_service.update_price errors: true
    order.save
    update_order_tasker
    Workers::OrderGeocoder.new(order.id.to_s).run(force: true)
  end

  private

  def order_service
    @order_service ||= OrderService.new(order)
  end

  def update_order_tasker
    order.order_taskers.each do |order_tasker|
      tasker = order_tasker.tasker
      preferential_params = { preferential_id: tasker.id.to_s }
      preferential_params.merge! preference_days(order_tasker) if order_tasker.requester_preference_days.any?

      order_service.remove_preferential(tasker, block_tasker: false)
      order_service.update_preferential(preferential_params) if preferential_candidates.include?(tasker)
    end
  end

  def preferential_candidates
    @preferential_candidates ||= OrderService::AvailablePreferentialTaskers.new(order:).get
  end

  # To check the contract, go to {OrderService::Preferential}.
  #
  def preference_days(order_tasker)
    {
      date: order_tasker.requester_preference_days.each_with_index.to_h do |pref_day, index|
        [index.to_s, { 'week_day' => pref_day[:wday], 'hour' => pref_day[:hour] }]
      end
    }
  end
end
