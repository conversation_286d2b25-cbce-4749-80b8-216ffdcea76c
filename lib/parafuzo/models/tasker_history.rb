# frozen_string_literal: true

# This model accounts the tasker's history for concluded jobs, penalty and background check.
#
class TaskerHistory
  include Mongoid::Document
  include Mongoid::Timestamps

  embedded_in :tasker

  field :completed_jobs, type: Integer, default: 0
  field :completed_preferential_jobs, type: Integer, default: 0
  field :completed_single_jobs, type: Integer, default: 0
  field :penalty, type: Float, default: 0.0

  field :partial_completed_jobs, type: Integer, default: 0
  field :partial_completed_preferential_jobs, type: Integer, default: 0
  field :partial_completed_single_jobs, type: Integer, default: 0
  field :partial_fidelity_points, type: Integer, default: 0
  field :last_job_discount_at, type: DateTime

  field :partial_penalty, type: Float, default: 0.0
  field :last_penalty_discount_at, type: DateTime

  field :background_check_last_result, type: String
  field :background_check_last_score, type: Integer
  field :background_check_status, type: String, default: 'unchecked'
  field :background_checked_at, type: DateTime

  field :photo_identity_checked_at, type: DateTime
  field :photo_identity_transaction_id, type: String
  field :photo_identity_transaction_created_at, type: DateTime
  field :photo_identity_score, type: Float
  field :photo_identity_score_received_at, type: DateTime
  field :photo_identity_attempts, type: Integer, default: 0
  field :photo_identity_state, type: String, default: 'unprocessed'

  before_save :increment_photo_identity_attempts

  def photo_identity_approved? = %w[approved approved_by_exception].include?(photo_identity_state)

  def photo_identity_check_state = photo_identity_checked_at.present? ? 'checked' : 'unchecked'

  def photo_identity_score_received? = photo_identity_score_received_at.present?

  private

  def increment_photo_identity_attempts
    return unless photo_identity_score_received_at_changed?
    return unless photo_identity_score_received_at_was.nil? && photo_identity_score_received_at.present?

    self.photo_identity_attempts += 1
  end
end
