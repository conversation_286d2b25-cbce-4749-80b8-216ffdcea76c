# frozen_string_literal: true

require 'aasm'
require 'active_support/time'
require 'enumerate_it'
require 'geocoder/models/mongoid'
require 'mongoid'
require 'mongoid_search'

require_relative '../core/integration_tracker'
require_relative 'enums/genders'
require_relative 'support/authenticable'
require_relative 'support/email_normalizable'
require_relative 'validators/tasker_verify_validator'
require_relative 'bank'
require_relative 'coordinates'
require_relative 'device'
require_relative 'feedback'
require_relative 'incident'
require_relative 'phone'
require_relative 'photo'
require_relative 'photo_default'
require_relative 'processed_feedback'
require_relative 'tasker_company'
require_relative 'tasker_history'
require_relative 'tasker_history_job'
require_relative 'tasker_flag'
require_relative 'service'
require_relative 'offer'

# Model to persists taskers informations.
#
class Tasker
  ACTIVE_STATES = %w[onboarding enabled].freeze
  AVAILABLE_STATES = %w[onboarding enabled suspended].freeze
  AUTHENTICABLE_STATES = %w[onboarding enabled suspended].freeze
  CHATABLE_STATES = %w[onboarding enabled suspended].freeze
  ZONES                     = %i[north south east west].freeze
  DAYS                      = %i[sunday monday tuesday wednesday thursday friday saturday].freeze
  MARITAL_STATUS            = %i[single married divorced widower].freeze
  PHONE_BRAND               = %i[windows android iphone other].freeze
  EDUCATION                 = %i[basic middle higher graduate].freeze
  PLANS                     = %i[basic gold diamond].freeze
  MAX_RECURRENCY_NO_SHOW    = 30
  MAX_RECURRENCY_DELAYED    = 15
  BLOCK_DAYS                = { no_show: 14, delayed: 2 }
  NO_SHOW_CANCEL_LIMIT      = 3.hours
  LOWER_SUBSCRIPTIONS_CATEGORIES = %w[uncategorized bronze].freeze
  TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS = ENV.fetch('TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS', '1.0').to_f

  include Mongoid::Document
  include Mongoid::Timestamps
  include Mongoid::Search
  include Geocoder::Model::Mongoid
  include Parafuzo::Model::Coordinates
  include Parafuzo::Core::IntegrationTracker
  include AASM
  include Authenticable
  include EmailNormalizable

  extend EnumerateIt

  before_validation :format_cpf
  before_save :reset_company

  has_many :feedbacks
  has_many :processed_feedbacks
  has_many :incidents
  has_many :offers
  has_many :events
  has_many :entries
  has_many :devices
  has_many :admin_notes, foreign_key: :resource_id
  has_many :tasker_feedbacks, foreign_key: :author_id
  has_many :deals
  has_many :history_jobs, class_name: 'TaskerHistoryJob'

  has_and_belongs_to_many :services, inverse_of: nil

  embeds_one  :address
  embeds_one  :history, class_name: 'TaskerHistory'
  embeds_one  :company, class_name: 'TaskerCompany', autobuild: true
  embeds_many :phones , as: :phonable     , cascade_callbacks: true
  embeds_many :banks  , as: :holder       , cascade_callbacks: true
  embeds_many :photos , as: :photographic , cascade_callbacks: true
  embeds_many :flags, cascade_callbacks: true, class_name: 'TaskerFlag'

  accepts_nested_attributes_for :address, :phones, :banks, :photos, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :flags

  field :name,              type: String
  field :birthdate,         type: DateTime
  field :email,             type: String
  field :zones,             type: Array, default: []
  field :active,            type: Boolean
  field :coordinates,       type: Array
  field :gender,            type: String
  field :interest_address,  type: String
  field :cpf,               type: String
  field :rg,                type: String
  field :notes,             type: String
  field :score,             type: Float, default: 3.0
  field :scores,            type: Hash , default: {}
  field :indication,        type: String
  field :availability,      type: Array
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :metadata,          type: Hash,    default: {}
  field :state,             type: String,  default: 'lead'
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :gateway_state,     type: String,  default: 'pending'
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :updating_bank,     type: Boolean, default: false
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :valid_account,     type: Boolean, default: true
  field :accepts_payout,    type: Boolean, default: true
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :gateway_errors,    type: Hash, default: {}
  field :blocked_until,     type: DateTime
  field :only_preferred,    type: Boolean
  field :csv_import_line,   type: Array
  field :marital_status,    type: Symbol
  field :has_children,      type: Boolean
  field :education,         type: Symbol
  field :phone_brand,       type: Symbol
  field :test_score,        type: Float
  field :mei,               type: String
  field :mei_password,      type: String
  field :pets_allowed,      type: Boolean, default: true
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :payment_frequency, type: Symbol,  default: :weekly
  field :payable_by_cash,   type: Boolean, default: false
  field :blumpa_password,   type: String,  default: nil

  field :blumpa_id         , type: String,   default: nil
  field :blumpa_migrated_at, type: DateTime, default: nil

  # FIXME: deprecated, moved to devices. must migrate data
  #
  field :notify_token       , type: String
  field :notify_type        , type: Symbol, default: :disabled
  field :notify_app_version , type: String

  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :stripe_account_id      , type: String
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :stripe_secret_key      , type: String # DEPRECATED: doesn't used anymore because we use account.type: 'custom'
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :stripe_publishable_key , type: String # DEPRECATED: doesn't used anymore because we use account.type: 'custom'
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :stripe_gateway_error   , type: String
  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  field :stripe_migrated        , type: Boolean
  field :political_exposure, type: String
  field :political_exposure_updated_at, type: DateTime

  # Nota de comprometimento
  field :commitment_score_recalculation, type: Boolean, default: false
  field :commitment_score              , type: Float  , default: 0

  # Diaria Turbo
  # TODO: Removes after version without premium payout stable.
  field :premium_payout_at , type: Date
  # TODO: Removes after version without premium payout stable.
  field :premium_payout_plan, type: Symbol

  field :plan, type: Symbol, default: :basic
  field :category, type: String, default: 'uncategorized'

  # Feedbacks do sistema
  field :system_feedback_agreement_accepted_at, type: DateTime

  # Authentication tracking
  field :current_sign_in_at, type: Time # A timestamp updated when the user signs in
  field :last_sign_in_at, type: Time # Holds the timestamp of the previous sign in
  field :current_sign_in_ip, type: String # The remote ip updated when the user sign in
  field :last_sign_in_ip, type: String # Holds the remote ip of the previous sign in

  geocoded_by :interest_address

  search_in :name, :zones, :email, :state, :translated_state, :cpf, :rg, services: %i[name human_name]

  has_enumeration_for :gender, with: Genders, create_helpers: true, create_scopes: true

  validates_presence_of :name
  validates :plan, presence: true, inclusion: { in: PLANS }

  validates :cpf, presence: true
  validate :validate_cpf_number
  validates_uniqueness_of :cpf
  validates_with TaskerVerifyValidator, on: :verification

  validates :birthdate, presence: true, if: -> { state.in? AVAILABLE_STATES }
  validates :interest_address, presence: true, if: -> { state.in? AVAILABLE_STATES }
  validates :latitude, presence: true, if: -> { state.in? AVAILABLE_STATES }
  validates :longitude, presence: true, if: -> { state.in? AVAILABLE_STATES }

  scope :recently_working, -> { where(:'history.partial_completed_jobs'.gt => 0) }
  scope :authenticable, -> { where(:state.in => AUTHENTICABLE_STATES) }

  index({ coordinates: '2d'}, { background: true, min: -180, max: 180 })
  index({ cpf: 1 }, { background: true })
  index({ name: 1, email: 1 }, { background: true })
  index({ 'phones.sms_number' => 1 })
  index({ state: 1, 'company.state': 1 }, { background: true })
  index({ 'history.photo_identity_transaction_id': 1 }, { background: true })

  aasm(:state, column: :state) do
    state :lead, initial: true
    state :onboarding
    state :enabled
    state :suspended
    state :disabled

    event :onboard do
      transitions from: :lead, to: :onboarding
    end

    event :enable do
      transitions to: :enabled
    end

    event :disable do
      transitions to: :disabled
    end
  end

  # TODO: Marked to remove, soon, with all Stripe and Iugu payments source code.
  aasm(:gateway_state, column: :gateway_state) do
    state :pending, initial: true
    state :verifying
    state :verified
    state :rejected

    event :request_verification do
      transitions from: :pending, to: :verifying
    end

    event :verify do
      transitions to: :verified
    end

    event :reject do
      transitions to: :rejected
    end
  end

  def bank = banks.detect(&:main?) || banks.first

  def can_make_cash_jobs? = payable_by_cash && enabled?

  def completed_documentation_for_mei? = mei.present? && mei_password.present?

  def device_login!(opts = {}) = devices.find_or_initialize_by(uuid: opts[:uuid]).login!(opts.except(:uuid))

  def main_device = devices.find_by(active: true)

  def makes_service?(service_name) = services.map(&:name).include? service_name

  def notify_push! = update notify_type: :push

  def notify_disabled! = update notify_type: :disabled

  def phone = phones.detect { |p| p.main? and !p.is_land_line? } || phones.detect { |p| !p.is_land_line? }

  def photo = photos.count.zero? ? PhotoDefault.new(:tasker) : photos.last

  def score_for(service) = scores[service].try(:[], :score).to_f

  def score_total_for(service) = scores[service].try(:[], :total).to_i

  def turbo_score_counter_for(service) = scores[service].try(:[], :turbo_score_counter).to_i

  def active_name
    active? ? I18n.t(:active) : I18n.t(:inactive)
  end

  def active?
    ACTIVE_STATES.include? state
  end

  def last_job
    Job.where(
      'job_taskers.tasker_id' => self.id,
      :state.nin => [:cancelled]
    ).order_by(:date.desc).first
  end

  def preferential_orders
    Order.where(
      :state.in => Order::VALID_CHECKOUT_STATES,
      subscription: true,
      order_taskers: { '$elemMatch' => { tasker_id: id, :confirmed_at.ne => nil, :rejected_at => nil } }
    )
  end

  def generate_new_password
    random_password.tap { |new_password| update_attributes password: new_password }
  end

  def job_taskers_from(jobs:)
    return [] if jobs.blank?

    jobs.flat_map(&:job_taskers).select { |jt| jt.tasker_id == id }
  end

  def lower_category?
    category.blank? ? true : LOWER_SUBSCRIPTIONS_CATEGORIES.any?(category)
  end

  def furniture_assembler?
    return false if services.blank?

    services.size == 1 && services.first.name == :furniture_assembly
  end

  def receives_preferential_offers?
    !!flags.find { _1[:name] == 'receive_preferential_offers' }&.enabled?
  end

  # @note The model is being directly called below with none because there is probably a bug in calling
  #   processed_feedbacks.none where the #count method and others stop working. This behavior
  #   were not possible to reproduce in automated tests, unfortunately.
  #
  def processed_feedbacks_for(service)
    return ProcessedFeedback.none if processed_feedbacks_disabled?

    service = service.to_sym
    return ProcessedFeedback.none unless makes_service? service

    processed_feedbacks.enabled.where(service:)
  end

  def processed_feedbacks_disabled?
    cut = (TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS * (2.pow(32) - 1))
    crc = Zlib.crc32(id.to_s)

    crc > cut
  end

  def translated_state = self.class.human_attribute_name("state/#{state}")

  def fidelity_rate
    return 0.0 if history.partial_completed_jobs.zero?

    (history.partial_fidelity_points.to_f / history.partial_completed_jobs).round 2
  end

  private

  def format_cpf
    self.cpf = CPF.new(cpf, strict: true).formatted
  end

  def validate_cpf_number
    errors.add(:cpf, :invalid_document) unless CPF.valid? cpf
  end

  def random_password
    words = %w(comida cachorro sapato formiga vitamina vestido sorriso dinheiro laranja vermelho amarelo branco)
    "#{words.sample}#{rand.to_s[2..4]}"
  end

  def reset_company
    return unless mei_changed?

    company.state = 'unchecked'
    company.active = false
    company.result_message, company.checked_at = nil
  end
end
