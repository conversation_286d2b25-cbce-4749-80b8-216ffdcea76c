# frozen_string_literal: true

FactoryBot.define do
  factory :tasker do
    email { Faker::Internet.email }
    name { Faker::Name.name }
    phones { [build(:phone, number: '11 99999-9999', main: true)] }
    scores { { cleaning: { score: 5, total: 0 }, business_cleaning: { score: 5, total: 0 } } }
    coordinates { [-1, -1] }
    active { true }
    services { [create(:service, :cleaning), create(:service, :business_cleaning)] }
    availability { Tasker::DAYS }
    birthdate { Date.parse('Jan 1, 1987') }
    rg { Faker::IdNumber.brazilian_id(formatted: true) }
    cpf { CPF.generate(true) }
    gender { Genders::FEMALE }
    state { :enabled }
    with_interest_address
    notify_app_version { '2.0' }
    last_sign_in_at { 2.days.ago }
    last_sign_in_ip { Faker::Internet.ip_v4_address }
    current_sign_in_at { 1.day.ago }
    current_sign_in_ip { Faker::Internet.ip_v4_address }

    history { build :tasker_history, completed_jobs: 3 } # rubocop:disable FactoryBot/FactoryAssociationWithStrategy

    trait :with_photo do
      photos { [build(:photo)] }
    end

    trait :with_flag do
      transient do
        flag_name { 'bring_products' }
        flag_allowed { true }
        flag_accepted { true }
      end

      flags { build_list :tasker_flag, 1, name: flag_name, allowed: flag_allowed, accepted: flag_accepted }
    end

    trait :with_flags do
      transient do
        bring_products_allowed { true }
        bring_products_accepted { true }

        receive_preferential_offers_allowed { true }
        receive_preferential_offers_accepted { true }
      end

      flags do
        [
          build(:tasker_flag, name: 'bring_products',
                              allowed: bring_products_allowed,
                              accepted: bring_products_accepted),
          build(:tasker_flag, name: 'receive_preferential_offers',
                              allowed: receive_preferential_offers_allowed,
                              accepted: receive_preferential_offers_accepted)
        ]
      end
    end

    trait :with_all_services do
      services do
        [
          create(:service, :cleaning),
          create(:service, :business_cleaning),
          create(:service, :express_cleaning),
          create(:service, :heavy_cleaning),
          create(:service, :pre_moving_cleaning),
          create(:service, :remodeling_cleaning),
          create(:service, :ironing),
          create(:service, :laundry),
          create(:service, :furniture_assembly)
        ]
      end
    end

    trait :with_email_photo do
      photos { [build(:photo, :email)] }
    end

    trait :inactive do
      active { false }
    end

    trait :verifiable do
      political_exposure
      with_interest_address

      banks { [build(:bank)] }
      rg { '*********' }
      address { build(:address) }
      gateway_state { 'pending' }
    end

    trait :with_interest_address do
      interest_address { address&.to_geocode.presence || 'São Paulo, SP' }
      coordinates { [-23.533773, -46.625290] }
    end

    trait :verified do
      verifiable

      gateway_state { 'verified' }
      metadata do
        { iugu: {
          account_id: 'D5A3F2D8ABF84D55B67A9A149A3C8B6C',
          name: 'Maria de Jesus',
          user_token: '6ec03237080ccc54f49e8b0dd340d0d5',
          live_api_token: 'e0b403871c0df025bba011110190f899',
          test_api_token: 'ea4c329dee9a3fe8e6b386a36a1a3e26'
        } }
      end
      stripe_account_id { 'stripe_account_id' }
    end

    trait :stripe_verified do
      banks { [build(:bank, stripe_id: 'stripe_id')] }
      rg { '*********' }
      address { build(:address) }
      gateway_state { 'verified' }
      stripe_account_id { 'stripe_account_id' }
    end

    trait :lead do
      state { 'lead' }
    end

    factory :lead_tasker, traits: %i[lead]

    trait :onboarding do
      state { 'onboarding' }
      with_interest_address
    end

    factory :onboarding_tasker, traits: %i[onboarding]

    trait :enabled do
      state { 'enabled' }
      with_interest_address
    end

    factory :enabled_tasker, traits: %i[enabled]

    trait :suspended do
      state { 'suspended' }
      with_interest_address
    end

    factory :suspended_tasker, traits: %i[suspended]

    trait :disabled do
      state { 'disabled' }
    end

    factory :disabled_tasker, traits: %i[disabled]

    trait :with_logged_device do
      after(:create) do |tasker, _evaluator|
        tasker.devices = [create(:device, :logged, tasker: tasker)]
      end
    end

    trait :with_unlogged_device do
      after(:create) do |tasker, _evaluator|
        tasker.devices = [create(:device, :unlogged, tasker: tasker)]
      end
    end

    trait :with_multiple_devices do
      after(:create) do |tasker, _evaluator|
        tasker.devices = [
          create(:device, :logged, tasker: tasker),
          create(:device, :unlogged, tasker: tasker)
        ]
      end
    end

    trait :payable_by_cash do
      payable_by_cash { true }
    end

    trait :political_exposure do
      political_exposure { 'existing' }
      political_exposure_updated_at { Time.current }
    end

    trait :with_scores do
      scores do
        {
          'cleaning' => { 'score' => 4.9, 'total' => 10 },
          'business_cleaning' => { 'score' => 4.95, 'total' => 12 }
        }
      end
    end

    trait :with_scores_of_all_available_services do
      scores do
        {
          'pre_moving_cleaning' => { 'score' => 4.9, 'total' => 10 },
          'furniture_assembly' => { 'score' => 4.95, 'total' => 12 },
          'express_cleaning' => { 'score' => 4.9, 'total' => 10 },
          'business_cleaning' => { 'score' => 4.95, 'total' => 12 },
          'cleaning' => { 'score' => 4.9, 'total' => 10 },
          'heavy_cleaning' => { 'score' => 4.95, 'total' => 12 },
          'remodeling_cleaning' => { 'score' => 4.95, 'total' => 12 },
          'ironing' => { 'score' => 4.95, 'total' => 12 },
          'laundry' => { 'score' => 4.9, 'total' => 10 }
        }
      end
    end

    trait :without_category do
      category { 'uncategorized' }
    end

    trait :with_bronze_category do
      category { 'bronze' }
    end

    trait :with_silver_category do
      category { 'silver' }
    end

    trait :with_gold_category do
      category { 'gold' }
    end

    factory :political_exposure_tasker do
      political_exposure
    end

    trait :with_sendbird_integration do
      integrations do
        { sendbird: { id: 'user:id', created_at: Time.current.iso8601, updated_at: Time.current.iso8601 } }
      end
    end
  end
end
