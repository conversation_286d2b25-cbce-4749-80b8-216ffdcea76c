# frozen_string_literal: true

require 'spec_helper'

describe Api::V2::Tasker do
  subject(:body) { JSON.parse(last_response.body) }

  describe 'GET /taskers/preferentials' do
    let(:options) do
      { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } }
    end

    let(:tasker) { create :tasker }
    let(:cleaning) { create :service, :cleaning }

    context 'when exists preferential customers' do
      before do
        create :job, order:, user: order.user, service: :cleaning
        create :job, order: second_order, user: second_order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      let(:second_order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 2.days.ago)],
               state: 'approved'
      end

      let :result do
        { 'data' => [
          { 'user' => { 'name' => order.user.name },
            'order' => {
              'id' => order.id.to_s,
              'frequency' => order[:parameters].select { _1['name'] == :subscription_type }
                                               .first['value']['value']
            } },
          { 'user' => { 'name' => second_order.user.name },
            'order' => {
              'id' => second_order.id.to_s,
              'frequency' => second_order[:parameters].select { _1['name'] == :subscription_type }
                                                      .first['value']['value']
            } }
        ] }
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data'].size).to eq 2 }
      it { expect(body).to match_array(result) }
    end

    context 'when does not exist preferential customer' do
      before { get '/taskers/preferentials', nil, options }

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']).to be_empty }
    end

    context 'when order_tasker is rejected' do
      before do
        create :job, order:, user: order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 4.days.ago, rejected_at: Time.current)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']).to be_empty }
    end

    context 'when order is pending' do
      before do
        create :job, order:, user: order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'pending'
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']).to be_empty }
    end

    context 'when order unconfirmed' do
      before do
        create :job, order:, user: order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: nil)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']).to be_empty }
    end

    context 'when single order with preferential' do
      before do
        create :job, order:, user: order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:order) do
        create :order,
               :cleaning_single,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: Time.current)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']).to be_empty }
    end

    context 'when tasker is not authenticated' do
      before do
        create :job, order:, user: order.user, service: :cleaning

        get '/taskers/preferentials', nil, options
      end

      let(:options) do
        { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => '' } }
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq(403) }
      it { is_expected.to eq({ 'message' => 'Not authorized' }) }
    end
  end

  describe 'GET /taskers/preferentials/:order_id' do
    let(:options) do
      { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } }
    end

    let(:tasker) { create :tasker }
    let(:cleaning) { create :service, :cleaning }
    let(:job_tasker) { build :job_tasker, tasker:, final_payout: 100, work_time: 7.0 }

    context 'when exists order' do
      before do
        create :job, order:, user: order.user, date: newest_date, service: :cleaning, job_taskers: [job_tasker]
        create :job, order:, user: order.user, date: earliest_date, service: :cleaning, job_taskers: [job_tasker]
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [other_job_tasker]

        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved',
               payout: 100.0,
               parameters: [
                 build(:order_detail, name: :requested_days, type: DetailTypes::SELECT, value: ['wednesday']),
                 build(:order_detail, name: :service_time, type: DetailTypes::TIME, value: '09:00'),
                 build(:order_detail, name: :requested_time, type: DetailTypes::INTEGER, value: 25),
                 build(:order_detail, name: :subscription_type, type: DetailTypes::SELECT, value: 'once_week')
               ]
      end

      let(:other_tasker) { create :tasker }
      let(:other_job_tasker) { build :job_tasker, tasker: other_tasker, final_payout: 100, work_time: 7.0 }

      let(:newest_date) { '2023-03-05T00:00:00-03:00' }
      let(:earliest_date) { '2023-03-03T00:00:00-03:00' }

      let(:taskers_per_job) { JobFactory.new(order).taskers_per_job }
      let(:next_job) { order.jobs.opened.where('job_taskers.tasker_id': tasker.id).asc(:date).first }
      let(:next_job_duration) { (next_job.work_time / taskers_per_job).round(2) }
      let(:bonus_per_tasker) { (order.payout / taskers_per_job * BonusService::PREFERENTIAL_PERCENTAGE).round(2) }

      let :result do
        {
          'data' => {
            'user' => { 'name' => order.user.name },
            'order' => {
              'job_template' => {
                'week_days' => ['wednesday'],
                'begin_at' => '09:00',
                'end_at' => (order.service_time + order.duration.round(2).hours).strftime('%H:%M'),
                'duration' => order.duration.round(2)
              },
              'address' => {
                'address' => order.address.address,
                'cep' => order.address.cep,
                'city' => order.address.city,
                'complement' => order.address.complement,
                'neighborhood' => order.address.neighborhood,
                'number' => order.address.number,
                'state' => order.address.state
              },
              'service' => order.service.to_s,
              'payout_value' => (order.payout / JobFactory.new(order).taskers_per_job).round(2),
              'bonus_value' => bonus_per_tasker,
              'frequency' => 'once_week',
              'next_job' => {
                'date' => next_job.date.to_date.to_s,
                'begin_at' => next_job.date.strftime('%H:%M'),
                'end_at' => (next_job.date + next_job_duration.hours).strftime('%H:%M'),
                'duration' => (job_tasker.work_time / taskers_per_job).round(2)
              }
            }
          }
        }
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body).to eq(result) }
    end

    context 'when exists order with an express cleaning service' do
      before do
        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:order) do
        create :order,
               :express_cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved',
               payout: 100.0
      end

      it { expect(body['data']['order']['bonus_value']).to eq(0.0) }
    end

    context 'when the order has one slots' do
      before do
        create(:job, order:, user: order.user, date: '2023-03-03T08:00:00-03:00', service: :cleaning,
                     job_taskers: [job_tasker_one])

        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 3.days.ago)],
               state: 'approved',
               payout: 200.0,
               parameters: [
                 build(:order_detail, name: :requested_days, type: DetailTypes::SELECT, value: ['wednesday']),
                 build(:order_detail, name: :service_time, type: DetailTypes::TIME, value: '13:00'),
                 build(:order_detail, name: :requested_time, type: DetailTypes::INTEGER, value: 5),
                 build(:order_detail, name: :subscription_type, type: DetailTypes::SELECT, value: 'once_week')
               ]
      end

      let(:job_tasker_one) { build :job_tasker, tasker:, work_time: 6 }

      it {  expect(body['data']['order']['payout_value']).to eq(200) }
      it {  expect(body['data']['order']['bonus_value']).to eq(20.0) }
      it {  expect(body['data']['order']['next_job']['begin_at']).to eq('08:00') }
      it {  expect(body['data']['order']['next_job']['end_at']).to eq('14:00') }
      it {  expect(body['data']['order']['next_job']['duration']).to eq(6) }
    end

    context 'when the order has 2 slots' do
      before do
        create(:job, order:, user: order.user, date: '2023-03-03T10:00:00-03:00', service: :cleaning,
                     job_taskers: [job_tasker_one, job_tasker_two])

        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [
                 build(:order_tasker, tasker:, confirmed_at: 3.days.ago),
                 build(:order_tasker, tasker: tasker_two, confirmed_at: 5.days.ago)
               ],
               state: 'approved',
               payout: 200.0,
               parameters: [
                 build(:order_detail, name: :requested_days, type: DetailTypes::SELECT, value: ['wednesday']),
                 build(:order_detail, name: :service_time, type: DetailTypes::TIME, value: '13:00'),
                 build(:order_detail, name: :requested_time, type: DetailTypes::INTEGER, value: 12),
                 build(:order_detail, name: :subscription_type, type: DetailTypes::SELECT, value: 'once_week')
               ]
      end

      let(:tasker_two) { create :tasker }
      let(:job_tasker_one) { build :job_tasker, tasker:, work_time: 4 }
      let(:job_tasker_two) { build :job_tasker, tasker: tasker_two }

      it { expect(body['data']['order']['payout_value']).to eq(100) }
      it { expect(body['data']['order']['bonus_value']).to eq(10.0) }
      it { expect(body['data']['order']['next_job']['begin_at']).to eq('10:00') }
      it { expect(body['data']['order']['next_job']['end_at']).to eq('14:00') }
      it { expect(body['data']['order']['next_job']['duration']).to eq(4) }
    end

    context 'when the next job belongs to another tasker' do
      before do
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [other_job_tasker]

        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved',
               payout: 100.0
      end

      let(:other_tasker) { create :tasker }
      let(:other_job_tasker) { build :job_tasker, tasker: other_tasker, final_payout: 100, work_time: 25.0 }

      it { expect(last_response.status).to eq 200 }
      it { expect(body['next_job']).to be_nil }
    end

    context 'when order no exists' do
      before { get '/taskers/preferentials/order_nonexistent', nil, options }

      it { expect(last_response.status).to eq 404 }
      it { expect(body).to be_empty }
    end

    context 'when no order related to current tasker' do
      before do
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [job_tasker]
        create :job, order: other_order, user: other_order.user, service: :cleaning, job_taskers: [other_job_tasker]

        get "/taskers/preferentials/#{other_order.id}", nil, options
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker: other_tasker, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      let(:other_order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker: other_tasker, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      let(:job_tasker) { build :job_tasker, tasker:, final_payout: 100, work_time: 4.0 }
      let(:other_job_tasker) { build :job_tasker, tasker: other_tasker, final_payout: 100, work_time: 4.0 }
      let(:other_tasker) { create :tasker }

      it { expect(last_response.status).to eq 404 }
      it { expect(body).to be_empty }
    end

    context 'when tasker is not authenticated' do
      before do
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [job_tasker]

        get "/taskers/preferentials/#{order.id}", nil, options
      end

      let(:options) do
        { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => '' } }
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq(403) }
      it { is_expected.to eq({ 'message' => 'Not authorized' }) }
    end
  end

  describe 'DELETE /taskers/preferentials/:order_id' do
    let(:options) do
      { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } }
    end

    let(:tasker) { create :tasker }
    let(:cleaning) { create :service, :cleaning }
    let(:job_tasker) { build :job_tasker, tasker:, final_payout: 100, work_time: 7.0 }
    let(:mailer) { instance_double Resque::Mailer::MessageDecoy, deliver: true }
    let(:event_tracker_payload) do
      {
        message: "tasker.preferentials.delete on Order:#{order.id}",
        author: { tasker_id: tasker.id.to_s },
        data: {
          order_id: order.id.to_s,
          user_id: order.user_id.to_s,
          tasker_id: tasker.id.to_s
        }
      }
    end

    def do_delete(order_id)
      delete "/taskers/preferentials/#{order_id}", nil, options
    end

    before do
      allow(TransactionalMailer).to receive(:preferential_tasker_removed).and_return mailer
      allow(Parafuzo::Core::EventTracker::Register.instance).to receive(:info)
    end

    context 'when exists preferential customers' do
      before do
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [job_tasker]
      end

      let(:order) { create :order, :cleaning_subscription, order_taskers: [order_tasker], state: 'approved' }
      let(:order_tasker) { build(:order_tasker, tasker:, confirmed_at: 5.days.ago) }

      it { expect { do_delete(order.id) }.to change { order.reload.order_taskers.count }.from(1).to(0) }
      it { expect(do_delete(order.id).status).to eq 200 }
      it { expect(JSON.parse(do_delete(order.id).body)).to eq 'data' => { 'message' => 'Preferential removed' } }

      it {
        do_delete(order.id)

        expect(TransactionalMailer).to have_received(:preferential_tasker_removed).with order.id.to_s, tasker.id.to_s
        expect(Parafuzo::Core::EventTracker::Register.instance).to have_received(:info).with(
          hash_including(event_tracker_payload), hash_including(labels: { type: 'tasker.preferentials.delete' })
        )
      }

      context 'with two order_taskers' do
        before do
          create :job, order:, user: order.user, date: Time.current, service: :cleaning,
                       job_taskers: [job_tasker_one, job_tasker_two]

          do_delete(order.id)
        end

        let(:options) do
          { 'HTTP_ACCEPT' => 'application/json+app',
            'rack.session' => { 'warden.user.tasker.key' => tasker_one.id.to_s } }
        end

        let(:order) do
          create :order, :cleaning_subscription, order_taskers: [order_tasker_one, order_tasker_two], state: 'approved'
        end

        let(:tasker_one) { create :tasker }
        let(:order_tasker_one) { build :order_tasker, tasker: tasker_one, confirmed_at: 5.days.ago }
        let(:job_tasker_one) { build :job_tasker, tasker: tasker_one, final_payout: 100, work_time: 7.0 }

        let(:tasker_two) { create :tasker }
        let(:order_tasker_two) { build :order_tasker, tasker: tasker_two, confirmed_at: 5.days.ago }
        let(:job_tasker_two) { build :job_tasker, tasker: tasker_two, final_payout: 100, work_time: 7.0 }

        it 'removes preferential from logged in tasker' do
          expect(order.reload.order_taskers).not_to include(order_tasker_one)
        end

        it 'not removes preferential from logged out tasker' do
          expect(order.reload.order_taskers).to include(order_tasker_two)
        end

        it {
          do_delete(order.id)

          expect(TransactionalMailer).to have_received(:preferential_tasker_removed).with(
            order.id.to_s, tasker_one.id.to_s
          )
        }

        it {
          do_delete(order.id)

          expect(TransactionalMailer).not_to have_received(:preferential_tasker_removed).with(
            order.id.to_s, tasker_two.id.to_s
          )
        }
      end
    end

    context 'when not exists preferential customers' do
      before { do_delete('order_id') }

      it { expect(last_response.status).to eq 404 }
      it { expect(body).to be_empty }

      it { expect(TransactionalMailer).not_to have_received(:preferential_tasker_removed) }
    end

    context 'when there is error deleting preferential customer' do
      before do
        allow(OrderService).to receive(:new).and_return(order_service)
        allow(order_service).to receive(:remove_preferential).and_return false

        create :job, order:, user: order.user, service: :cleaning, job_taskers: [job_tasker]

        do_delete(order.id)
      end

      let(:order_service) { instance_double(OrderService) }

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq 422 }
      it { expect(body).to be_empty }
      it { expect(TransactionalMailer).not_to have_received(:preferential_tasker_removed) }
    end

    context 'when tasker is not authenticated' do
      before do
        create :job, order:, user: order.user, service: :cleaning, job_taskers: [job_tasker]

        do_delete(order.id)
      end

      let(:options) do
        {
          'HTTP_ACCEPT' => 'application/json+app',
          'rack.session' => { 'warden.user.tasker.key' => '' }
        }
      end

      let(:order) do
        create :order,
               :cleaning_subscription,
               order_taskers: [build(:order_tasker, tasker:, confirmed_at: 5.days.ago)],
               state: 'approved'
      end

      it { expect(last_response.status).to eq(403) }
      it { is_expected.to eq({ 'message' => 'Not authorized' }) }
      it { expect(TransactionalMailer).not_to have_received(:preferential_tasker_removed) }
    end
  end

  describe 'GET /taskers/processed_feedbacks/:service_type' do
    let(:service_type) { 'cleaning' }
    let(:tasker) { create :tasker }
    let(:options) { { 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } } }

    context 'without authentication' do
      before { get "/taskers/processed_feedbacks/#{service_type}" }

      it { expect(last_response.status).to eq(403) }
      it { expect(body).to eq({ 'message' => 'Not authorized' }) }
    end

    context 'with authentication and without processed feedbacks' do
      before { get "/taskers/processed_feedbacks/#{service_type}", nil, options }

      it { expect(last_response.status).to eq(200) }
      it { expect(body['data']).to include 'processed_feedbacks' => [] }
      it { expect(body['data']).to include 'service' => service_type }
      it { expect(body['data']).to include 'score' => '5.00' }
    end

    context 'with authentication and processed feedbacks' do
      let!(:older_feedback) { create :processed_feedback, tasker:, service: :cleaning, created_at: 2.days.ago }
      let!(:newer_feedback) { create :processed_feedback, tasker:, service: :cleaning, created_at: 1.day.ago }

      before do
        create :processed_feedback, tasker:, service: :express_cleaning
        create :processed_feedback, tasker:, service: :heavy_cleaning

        get "/taskers/processed_feedbacks/#{service_type}", nil, options
      end

      it {
        expect(body['data']['processed_feedbacks'].pluck('id')).to eq [newer_feedback.id.to_s, older_feedback.id.to_s]
      }
    end

    context 'when the tasker does not perform the service' do
      let(:service_type) { 'heavy_cleaning' }

      before { get "/taskers/processed_feedbacks/#{service_type}", nil, options }

      it { expect(last_response.status).to eq(200) }
      it { expect(body['data']).to include 'processed_feedbacks' => [] }
      it { expect(body['data']).to include 'service' => service_type }
      it { expect(body['data']).to include 'score' => '0.00' }
    end
  end

  describe 'GET /taskers/scores' do
    let(:options) do
      { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } }
    end

    let(:scores) do
      {
        punctuality: { score: 4.96, total: 114 },
        friendliness: { score: 4.96, total: 114 },
        heavy_cleaning: { score: 5.00, total: 31 },
        cleaning: { score: 4.94, total: 135 }
      }
    end

    let(:tasker) { create :tasker, scores: scores }

    before do
      create :processed_feedback, tasker:, service: :cleaning
      get '/taskers/scores', nil, options
    end

    context 'when scores are available' do
      let(:expected_response) do
        { 'data' =>
          {
            'scores' => [
              { 'service' => 'punctuality', 'score' => '4.96', 'total' => 114 },
              { 'service' => 'friendliness', 'score' => '4.96', 'total' => 114 },
              { 'service' => 'heavy_cleaning', 'score' => '5.00', 'total' => 31, 'processed_feedbacks' => false },
              { 'service' => 'cleaning', 'score' => '4.94', 'total' => 135, 'processed_feedbacks' => true }
            ]
          } }
      end

      it { expect(last_response.status).to eq 200 }
      it { expect(body).to eq expected_response }
    end

    context 'when no scores are available' do
      let(:scores) { {} }

      it { expect(last_response.status).to eq 200 }
      it { expect(body['data']['scores']).to be_empty }
    end

    context 'when tasker is not authenticated' do
      let(:options) do
        { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => '' } }
      end

      let(:scores) { {} }

      it { expect(last_response.status).to eq 403 }
      it { expect(body).to eq({ 'message' => 'Not authorized' }) }
    end
  end
end
