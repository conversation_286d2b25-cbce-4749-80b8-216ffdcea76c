# frozen_string_literal: true

require 'spec_helper'

describe Api::Tasker do
  let(:body)    { JSON.parse last_response.body }
  let(:options) { {} }

  context 'GET /taskers/:id' do
    let(:tasker) { create :tasker }

    it_behaves_like :block_multiple_devices, { verb: :get, path: '/taskers/:id' }

    def do_get(tasker_id = tasker.id)
      get "/taskers/#{tasker_id}"
    end

    def do_get_app(tasker_id = tasker.id)
      get "/taskers/#{tasker_id}", {}, { 'HTTP_ACCEPT' => 'application/json+app' }
    end

    def do_get_search(tasker_id = tasker.id)
      get "/taskers/#{tasker_id}", {}, { 'HTTP_ACCEPT' => 'application/json+search' }
    end

    context 'with autentication' do
      before { authorize 'parafuzo', 'mudar123' }

      it 'responds 200' do
        expect(TaskerDecoratorApi).to receive(:new).with(tasker, {}).and_return tasker
        do_get
        expect(last_response.status).to eq 200
      end

      it 'responds to app' do
        expect(TaskerDecoratorApp).to receive(:new).with(tasker, {}).and_return tasker
        do_get_app
        expect(last_response.status).to eq 200
      end

      it 'responds 404 when not exist' do
        do_get 'invalid-tasker'
        expect(last_response.status).to eq 404
      end
    end

    context 'without authentication' do
      it 'responds 403' do
        do_get
        expect(last_response.status).to eq 403
      end
    end
  end

  context 'GET /taskers/me' do
    let(:options) { { 'HTTP_X_APP_VERSION' => '2.3.0', 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } } }
    let(:tasker)  { create :tasker, :with_interest_address }

    it_behaves_like :block_multiple_devices, { verb: :get, path: '/taskers/me' }, strict_authentication: true

    def do_get
      get '/taskers/me', {}, options
    end

    it 'updates tasker app version' do
      tasker_app_version = tasker.notify_app_version

      do_get
      tasker.reload

      expect(tasker.notify_app_version).not_to eq tasker_app_version
    end

    it 'is successful' do
      do_get
      expect(last_response.status).to eq(200)
    end

    it 'returns tasker' do
      do_get
      expect(body['id']).to eq tasker.id.to_s
    end

    it {
      do_get

      expect(body['flags']).to eq({
                                    'bring_products' => { 'accepted' => false, 'disabled' => true },
                                    'receive_preferential_offers' => { 'accepted' => true, 'disabled' => true }
                                  })
    }

    context 'unauthenticated' do
      let(:options) { {} }

      it 'returns 403' do
        do_get
        expect(last_response.status).to eq(403)
      end
    end

    context 'when there are flags' do
      before do
        create :tasker_flag, tasker:, accepted: false
        create :tasker_flag, :receive_preferential_offers, tasker:
      end

      it {
        do_get

        expect(body['flags']).to eq({
                                      'bring_products' => { 'accepted' => false, 'disabled' => true },
                                      'receive_preferential_offers' => { 'accepted' => false, 'disabled' => true }
                                    })
      }
    end

    context 'feedback' do
      context 'no feedbacks' do
        it { do_get; expect(body['last_feedback_received_at']).to eq(nil) }
      end

      context 'with user feedbacks' do
        let(:time) { 1.day.ago }
        let!(:feedbacks) do
          [
            create(:feedback, updated_at: time, tasker: tasker, score: 5, review: 'uhu', state: :reviewed, id: 'id'),
            create(:feedback, updated_at: 2.days.ago, tasker: tasker, score: 5, review: 'uhu2', state: :reviewed, id: 'id2')
          ]
        end

        it { do_get; expect(body['last_feedback_received_at']).to eq(time.utc.as_json) }
      end
    end

    it 'returns region of interest' do
      create :stored_config, :sms

      do_get

      expect(body['region_of_interest']).to include('description' => tasker.interest_address, 'radius_in_km' => 30.0,
                                                    'longitude' => tasker.longitude, 'latitude' => tasker.latitude)
    end

    context 'when login in stealth mode' do
      let(:options) do
        { 'HTTP_X_DEVICE_UUID' => 'pfz147172b0224e4c489be29f860bdae41epfz', 'HTTP_X_APP_VERSION' => '2.3.0',
          'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } }
      end

      it 'does not update tasker app version' do
        expect { do_get }.not_to(change { tasker.reload.notify_app_version })
      end
    end
  end

  context '/taskers/last_feedbacks' do
    let(:options) { { 'HTTP_ACCEPT' => 'application/json+app', 'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s } } }
    let(:tasker)  { create :tasker }

    it_behaves_like :block_multiple_devices, { verb: :get, path: '/taskers/last_feedbacks' }, strict_authentication: true

    def do_get(params = {})
      get '/taskers/last_feedbacks', params, options
    end

    before do
      create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 4, review: nil, state: :reviewed
      create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 4, review: '', state: :reviewed
      create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 4, review: '', state: :scored
      @feedback = create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 5, friendliness: 5, review: 'uhu',
                                           state: :reviewed, id: 'id'
      create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 3, review: 'meh', state: :reviewed
      create :feedback, :user, updated_at: 4.days.ago, tasker:, score: 0, review: '', state: :requested
      create :feedback, :user, updated_at: 20.days.ago, tasker:, score: 4, review: 'uhu 2', state: :reviewed
      create :feedback, :user, updated_at: 6.days.ago, tasker:, score: 5, friendliness: 5, review: 'jih',
                               state: :reviewed
      create :feedback, :user, updated_at: 59.days.ago, tasker:, score: 5, friendliness: 5, review: 'ahh',
                               state: :reviewed
      create :feedback, :user, updated_at: 60.days.ago, tasker:, score: 5, friendliness: 5, review: 'jow',
                               state: :reviewed
      create :feedback, :user, updated_at: 60.days.ago, tasker:, score: 4, review: 'lah', state: :reviewed
      create :feedback, :user, updated_at: 61.days.ago, tasker:, score: 5, friendliness: 5, review: 'lah',
                               state: :reviewed
      create :feedback, :user, updated_at: 62.days.ago, tasker:, score: 5, friendliness: 5, review: 'lop',
                               state: :reviewed
      create :feedback, :award, updated_at: 5.days.ago, tasker:, score: 5, friendliness: 5, review: 'turbo',
                                state: :reviewed
      create :feedback, :award, updated_at: 62.days.ago, tasker:, score: 5, review: 'amazing', state: :reviewed
    end

    it 'is success' do
      do_get
      expect(last_response.status).to eq(200)
    end

    it 'returns the correct data' do
      do_get
      feedback = body.first
      expect(feedback).to eq({
        'review' => 'uhu',
        'score' => 5,
        'id' => 'id',
        'type' => 'good',
        'created_at' => @feedback.created_at.to_time.utc.to_fs(:iso8601), # rubocop:disable RSpec/InstanceVariable
        'reviewed_at' => nil,
        'job' => { 'date' => @feedback.job.date.as_json, 'service' => @feedback.job.service.to_s },
        'service' => 'cleaning' })
    end

    it 'returns only counting of feedbacks that fit the criteria' do
      do_get
      expect(body.count).to eq 5
    end

    it 'returns list from feedbacks since 60 days ago and 5 points' do
      do_get
      review = body.map { |r| r['review'] }
      expect(review).to match_array(%w[uhu jih ahh jow turbo])
    end

    it 'returns reviewed feedbacks since 59 days ago and 5 points' do
      do_get(since: 59.days.ago.to_date)
      review = body.map { |r| r['review'] }
      expect(review).to match_array(%w[uhu jih ahh turbo])
    end

    it 'returns reviewed feedbacks since 5 days ago and 5 points' do
      do_get(since: 5.days.ago.to_date)
      review = body.map { |r| r['review'] }
      expect(review).to match_array(%w[uhu turbo])
    end

    it 'returns reviewed feedbacks since 63 days ago and 5 points' do
      do_get(since: 63.days.ago.to_date)
      review = body.map { |r| r['review'] }
      expect(review).to match_array(%w[uhu jih ahh jow lah lop turbo amazing])
    end

    context 'when there is only one feedback with updated date out of acomplished requirement' do
      let(:options) do
        { 'HTTP_ACCEPT' => 'application/json+app',
          'rack.session' => { 'warden.user.tasker.key' => tasker_with_legacy_job.id.to_s } }
      end
      let(:tasker_with_legacy_job) { create :tasker }

      before do
        create :feedback, :user, updated_at: 61.days.ago, tasker: tasker_with_legacy_job, score: 5,
                                 review: 'uhu legacy', state: :reviewed, job: create(:job, date: 62.days.ago)
      end

      it 'returns a empty list' do
        do_get
        expect(body).to be_empty
      end
    end
  end

  context 'PATCH /taskers/:id' do
    let(:options) do
      {
        'HTTP_X_APP_VERSION' => '1.15.1',
        'HTTP_ACCEPT' => 'application/json+app',
        'CONTENT_TYPE' => 'application/json',
        'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s }
      }
    end

    let(:tasker) { create :tasker }
    let(:params) { { push_token: 'token' } }

    it_behaves_like :block_multiple_devices, { verb: :patch, path: '/taskers/:id' }, run_edit_tests: true

    def do_patch
      patch "/taskers/#{tasker.id}", params.to_json, options
    end

    it 'is success' do
      do_patch
      expect(last_response.status).to eq 200
    end

    it 'updates tasker notify params' do
      do_patch
      tasker.reload

      expect(tasker.notify_type).to        eq :push
      expect(tasker.notify_token).to       eq 'token'
      expect(tasker.notify_app_version).to eq '1.15.1'
    end

    context 'with tasker availability' do
      let(:params) { { availability: ['friday'] } }
      let(:tasker) do
        create(
          :tasker, notify_type: :push, notify_token: '42', availability: ['monday']
        )
      end

      it { expect { do_patch }.to     change { tasker.reload.availability }.to(['friday']) }

      it { expect { do_patch }.not_to change { tasker.reload.notify_type }.from(:push) }
      it { expect { do_patch }.not_to change { tasker.reload.notify_token }.from('42') }
    end

    context 'when token is removed' do
      let(:params) { { push_token: '' } }
      let(:tasker) do
        create :tasker, notify_type: :push, notify_token: 'token maroto'
      end

      it { expect { do_patch }.to change { tasker.reload.notify_token }.to('') }
      it { expect { do_patch }.to change { tasker.reload.notify_type }.to(:disabled) }
    end

    context 'when pets_allowed is sent' do
      context 'and param is nil' do
        let(:params) { { pets_allowed: nil } }

        it { expect { do_patch }.not_to change { tasker.reload.pets_allowed }.from(true) }
      end

      context 'and pets are allowed' do
        let(:params) { { pets_allowed: true } }

        it { expect { do_patch }.not_to change { tasker.reload.pets_allowed }.from(true) }
      end

      context "and pets aren't allowed" do
        let(:params) { { pets_allowed: false } }

        it { expect { do_patch }.to change { tasker.reload.pets_allowed }.from(true).to(false) }
      end
    end

    context 'when gender is sent' do
      context 'and param is nil' do
        let(:params) { { gender: nil } }

        it { expect { do_patch }.not_to change { tasker.reload.gender }.from('f') }
      end

      context 'and param is blank' do
        let(:params) { { gender: '' } }

        it { expect { do_patch }.to change { tasker.reload.gender }.from('f').to('') }
      end

      context 'and gender is MALE' do
        let(:params) { { gender: 'm' } }

        it { expect { do_patch }.to change { tasker.reload.gender }.from('f').to('m') }
      end

      context 'and gender is FEMALE' do
        let(:params) { { gender: 'f' } }

        it { expect { do_patch }.not_to change { tasker.reload.gender }.from('f') }
      end
    end

    context 'when system_feedback_agreement_accepted_at is sent' do
      context 'and param is nil' do
        let(:params) { { system_feedback_agreement_accepted: nil } }

        it { expect { do_patch }.not_to change { tasker.reload.system_feedback_agreement_accepted_at } }
      end

      context 'and param is blank' do
        let(:params) { { system_feedback_agreement_accepted: '' } }

        it { expect { do_patch }.not_to change { tasker.reload.system_feedback_agreement_accepted_at } }
      end

      context 'and aggrement is accepted' do
        let(:params) { { system_feedback_agreement_accepted: true } }

        it { expect { do_patch }.to change { tasker.reload.system_feedback_agreement_accepted_at } }
      end

      context 'and aggrement is not accepted' do
        let(:params) { { system_feedback_agreement_accepted: false } }

        it { expect { do_patch }.not_to change { tasker.reload.system_feedback_agreement_accepted_at } }
      end
    end

    context 'when changing bring_products flag' do
      let(:params) { { flags: { bring_products: { accepted: false } } } }

      it {
        expect { do_patch }.to(
          change { tasker.reload.flags.find_by(name: 'bring_products') }.from(nil).to(an_instance_of(TaskerFlag))
        )
      }

      it {
        do_patch

        expect(tasker.reload.flags.find_by(name: 'bring_products').accepted).to be_falsey
      }

      context 'when the tasker already has the flag' do
        before { create :tasker_flag, tasker: }

        it {
          expect { do_patch }.to(
            change { tasker.reload.flags.find_by(name: 'bring_products').accepted }.from(true).to(false)
          )
        }

        context 'when there is no params to change it' do
          let(:params) { {} }

          it { expect { do_patch }.not_to(change { tasker.reload.flags.find_by(name: 'bring_products').accepted }) }
        end
      end
    end

    context 'when changing receive_preferential_offers flag' do
      let(:params) { { flags: { receive_preferential_offers: { accepted: false } } } }

      it {
        do_patch

        expect(tasker.reload.flags.find_by(name: 'receive_preferential_offers').accepted).to be_falsey
      }

      context 'when the tasker already has the flag' do
        before { create :tasker_flag, :receive_preferential_offers, tasker: }

        let(:params) { { flags: { receive_preferential_offers: { accepted: true } } } }

        it {
          expect { do_patch }.to(
            change { tasker.reload.flags.find_by(name: 'receive_preferential_offers').accepted }.from(false).to(true)
          )
        }

        context 'when there is no params to change it' do
          let(:params) { {} }

          it {
            expect { do_patch }
              .not_to(change { tasker.reload.flags.find_by(name: 'receive_preferential_offers').accepted })
          }
        end
      end
    end
  end

  context 'authentication' do
    let(:blumpa_password) { nil }
    let(:tasker) do
      create(
        :tasker, name: 'Maria', cpf: '053.054.499-79', password: 'password',
                 blumpa_password: blumpa_password
      )
    end

    context 'POST /tasker/login' do
      it_behaves_like :block_multiple_devices, { verb: :post, path: '/taskers/login' }, strict_authentication: true, run_login_tests: true, find_by: :cpf

      def do_post_blumpa_login
        post '/taskers/login', cpf: tasker.cpf, password: '123456'
      end

      context 'with valid credentials' do
        context 'correct password' do
          let(:blumpa_password) { '$2a$10$LJ9qaMvNBMNKb2ZtqU.yvOYJ6LO5Y296tZohlFpkqM0NB90x9ERCO' }

          it 'is success' do
            post '/taskers/login', cpf: tasker.cpf, password: 'password'
            expect(last_response.status).to eq 200
          end

          it 'returns tasker info' do
            post '/taskers/login', cpf: tasker.cpf, password: 'password'
            expect(body['id']).to eq tasker.id.to_s
            expect(body['name']).to eq 'Maria'
          end

          it 'and use blumpa password' do
            do_post_blumpa_login
            expect(last_response.status).to eq 200
          end

          it 'update password attribute when authenticates with blumpa password' do
            expect { do_post_blumpa_login }.to(change { tasker.reload.password })
          end

          it 'set as nil blumpa_password when logged in with blumpa password' do
            expect { do_post_blumpa_login }.to change { tasker.reload.blumpa_password }.to(nil)
          end
        end

        context 'master password' do
          let(:master_password) { 'master_password' }

          before do
            allow(ENV).to receive(:[]).and_call_original
            allow(ENV).to receive(:[]).with('TASKER_APP_MASTER_PASSWORD').and_return master_password
          end

          it 'is success' do
            post '/taskers/login', cpf: tasker.cpf, password: master_password
            expect(last_response.status).to eq 200
          end

          it 'returns tasker info' do
            post '/taskers/login', cpf: tasker.cpf, password: master_password
            expect(body['id']).to eq tasker.id.to_s
            expect(body['name']).to eq 'Maria'
          end
        end

        describe 'authentication tracking' do
          let(:headers) do
            {
              'HTTP_X_FORWARDED_FOR' => forwarded_ips, 'HTTP_X_APP_VERSION' => '2.3.0',
              'HTTP_X_DEVICE_UUID' => SecureRandom.hex, 'HTTP_ACCEPT' => 'application/json+app',
              'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s }
            }
          end
          let(:forwarded_ips) { [Faker::Internet.ip_v4_address, Faker::Internet.ip_v6_address].join(',') }
          let(:tasker) { create :tasker, name: 'Maria', cpf: '053.054.499-79', password: 'password' }

          def do_post
            post '/taskers/login', { cpf: tasker.cpf, password: 'password' }, headers
          end

          it 'sets current_sign_in_at and current_sign_in_ip to logged tasker' do
            do_post

            expect(tasker.reload).to have_attributes(
              current_sign_in_at: an_instance_of(ActiveSupport::TimeWithZone),
              current_sign_in_ip: forwarded_ips.split(',').first
            )
          end

          it 'sets last_sign_in_at and last_sign_in_ip from previous current_sign_in_at and current_sign_in_ip' do
            do_post

            last_sign_in_at = tasker.current_sign_in_at
            last_sign_in_ip = tasker.current_sign_in_ip

            expect(tasker.reload).to have_attributes(last_sign_in_at: be_within(1.second).of(last_sign_in_at),
                                                     last_sign_in_ip:)
          end

          context 'when forwarded ips is blank' do # rubocop:disable RSpec/NestedGroups
            let(:forwarded_ips) { '' }

            it 'sets current_sign_in_ip to nil when forwarded ips is blank' do
              do_post

              expect(tasker.reload).to have_attributes(current_sign_in_ip: nil)
            end
          end

          context 'when forwarded ips has a IPv6 as first IP' do # rubocop:disable RSpec/NestedGroups
            let(:forwarded_ips) { [Faker::Internet.ip_v6_address, Faker::Internet.ip_v4_address].join(',') }

            it 'sets current_sign_in_ip' do
              do_post

              expect(tasker.reload).to have_attributes(current_sign_in_ip: forwarded_ips.split(',').first)
            end
          end

          context 'when it is stealth mode' do # rubocop:disable RSpec/NestedGroups
            let(:headers) do
              {
                'HTTP_X_FORWARDED_FOR' => forwarded_ips, 'HTTP_X_APP_VERSION' => '2.3.0',
                'HTTP_X_DEVICE_UUID' => 'pfz147172b0224e4c489be29f860bdae41epfz',
                'HTTP_ACCEPT' => 'application/json+app',
                'rack.session' => { 'warden.user.tasker.key' => tasker.id.to_s }
              }
            end

            it 'does not change current_sign_in_at and current_sign_in_ip to logged tasker' do
              do_post

              expect(tasker.reload).to have_attributes(
                current_sign_in_at: tasker.current_sign_in_at,
                current_sign_in_ip: tasker.current_sign_in_ip
              )
            end
          end
        end
      end

      context 'with app content-type' do
        it 'returns the correct body' do
          post '/taskers/login', { cpf: tasker.cpf, password: 'password' }, { 'HTTP_ACCEPT' => 'application/json+app' }
          expect(body['id']).to eq tasker.id.to_s
          expect(body['name']).to eq 'Maria'
        end
      end

      context 'with invalid credentials' do
        it 'returns unauthorized status' do
          post '/taskers/login', cpf: tasker.cpf, password: 'wrong password'
          expect(last_response.status).to eq 403
        end
      end

      context 'with missing cpf' do
        it 'returns unauthorized status' do
          post '/taskers/login', cpf: '111.111.111-11', password: 'wrong password'
          expect(last_response.status).to eq 404
        end
      end

      context 'when tasker is lead' do
        before { tasker.update state: :lead }

        it 'returns not found' do
          post '/taskers/login', cpf: tasker.cpf, password: 'password'

          expect(last_response.status).to eq 404
        end
      end

      context 'when tasker is onboarding' do
        before { tasker.update state: :onboarding }

        it 'returns success' do
          post '/taskers/login', cpf: tasker.cpf, password: 'password'

          expect(last_response.status).to eq 200
        end
      end

      context 'when tasker is enabled' do
        before { tasker.update state: :enabled }

        it 'returns success' do
          post '/taskers/login', cpf: tasker.cpf, password: 'password'

          expect(last_response.status).to eq 200
        end
      end

      context 'when tasker is suspended' do
        before { tasker.update state: :suspended }

        it 'returns success' do
          post '/taskers/login', cpf: tasker.cpf, password: 'password'

          expect(last_response.status).to eq 200
        end
      end

      context 'when tasker is disabled' do
        before { tasker.update state: :disabled }

        it 'returns not found' do
          post '/taskers/login', cpf: tasker.cpf, password: 'password'

          expect(last_response.status).to eq 404
        end
      end
    end

    context 'DELETE /tasker/logout' do
      let(:tasker) { create :tasker, name: 'Maria', cpf: '053.054.499-79', password: 'password' }
      let(:options) { {} }

      it_behaves_like :block_multiple_devices, { verb: :delete, path: '/taskers/logout' }, strict_authentication: true, run_logout_tests: true

      def do_logout
        delete '/taskers/logout', {}, options
      end

      context 'when tasker is logged in' do
        let(:service) { double logout: true }

        before do
          allow(TaskerService).to receive(:new).with(tasker).and_return service
          post '/taskers/login', cpf: tasker.cpf, password: 'password'
        end

        it 'returns status 204' do
          do_logout
          expect(last_response.status).to eq 204
        end

        it 'calls tasker service' do
          expect(service).to receive(:logout)
          do_logout
        end
      end

      context 'when user is not logged in' do
        it 'returns unauthorized status' do
          do_logout
          expect(last_response.status).to eq 403
        end
      end

    end

    context 'POST /tasker/forget-password' do
      let(:mailer) { instance_double Resque::Mailer::MessageDecoy, deliver!: true }

      before do
        authorize 'parafuzo', 'mudar123'
        allow(TransactionalMailer).to receive(:tasker_forget_password).and_return mailer
      end

      context 'when email is valid' do
        let(:tasker) { create :tasker, name: 'Maria', cpf: '053.054.499-79', password: 'password' }

        before do
          allow(Tasker).to receive(:find_by).with(cpf: '053.054.499-79').and_return tasker
        end

        it 'calls generate new password' do
          expect(tasker).to receive(:generate_new_password).with(no_args)

          post '/taskers/forget-password', cpf: '053.054.499-79'
        end

        it 'calls TransactionalMailer correctly' do
          post '/taskers/forget-password', cpf: '053.054.499-79'

          expect(TransactionalMailer).to have_received(:tasker_forget_password)
        end

        it 'sets a password recovery token' do
          expect(tasker).to receive(:forget_password!)
          post '/taskers/forget-password', cpf: '053.054.499-79'
        end

        it 'sends email to tasker' do
          post '/taskers/forget-password', cpf: '053.054.499-79'

          expect(mailer).to have_received(:deliver!)
        end

        it 'returns a success messsage' do
          post '/taskers/forget-password', cpf: '053.054.499-79'
          expect(body['message']).to eq 'Email enviado com sucesso!'
        end

        context 'when tasker dont have email' do
          let(:tasker) { double id: 'tasker id', forget_password!: true, email: '' }

          it 'does not set a password recovery token' do
            expect(tasker).not_to receive(:forget_password!)
            post '/taskers/forget-password', cpf: '053.054.499-79'
          end

          it 'does not send email to user' do
            expect(mailer).not_to receive(:deliver)
            post '/taskers/forget-password', cpf: '053.054.499-79'
          end

          it 'returns fail message' do
            post '/taskers/forget-password', cpf: '053.054.499-79'
            expect(body['message']).to eq 'CPF não cadastrado ou email inválido.'
          end
        end
      end

      context 'when tasker is lead' do
        before { tasker.update state: :lead }

        it 'returns not found' do
          post '/taskers/forget-password', cpf: tasker.cpf

          expect(body['message']).to eq 'CPF não cadastrado ou email inválido.'
        end
      end

      context 'when tasker is onboarding' do
        before { tasker.update state: :onboarding }

        it 'returns not found' do
          post '/taskers/forget-password', cpf: tasker.cpf

          expect(TransactionalMailer).to have_received(:tasker_forget_password).with(tasker.id.to_s,
                                                                                     an_instance_of(String))
        end
      end

      context 'when tasker is enabled' do
        before { tasker.update state: :enabled }

        it 'returns success' do
          post '/taskers/forget-password', cpf: tasker.cpf

          expect(TransactionalMailer).to have_received(:tasker_forget_password).with(tasker.id.to_s,
                                                                                     an_instance_of(String))
        end
      end

      context 'when tasker is suspended' do
        before { tasker.update state: :suspended }

        it 'returns success' do
          post '/taskers/forget-password', cpf: tasker.cpf

          expect(TransactionalMailer).to have_received(:tasker_forget_password).with(tasker.id.to_s,
                                                                                     an_instance_of(String))
        end
      end

      context 'when tasker is disabled' do
        before { tasker.update state: :disabled }

        it 'returns not found' do
          post '/taskers/forget-password', cpf: tasker.cpf

          expect(body['message']).to eq 'CPF não cadastrado ou email inválido.'
        end
      end

      context 'when cpf is invalid' do
        before do
          allow(User).to receive(:find_by).with(cpf: 'xxx').and_return nil
        end

        it 'returns a fail messsage' do
          post '/taskers/forget-password', cpf: 'xxx'
          expect(body['message']).to eq 'CPF não cadastrado ou email inválido.'
        end
      end
    end

    context 'POST /tasker/reset-password' do
      before { authorize 'parafuzo', 'mudar123' }

      let(:tasker) { create :tasker, password: 'old password' }

      before { tasker.forget_password! }

      context 'when token is valid' do
        it 'returns a success messsage' do
          post '/taskers/reset-password', token: tasker.password_recovery, password: 'new password'
          expect(body['message']).to eq 'Senha alterada com sucesso!'
        end

        it 'sets the new password' do
          expect(Tasker.last.authenticate('old password')).to be_truthy
          post '/taskers/reset-password', token: tasker.password_recovery, password: 'new password'
          expect(Tasker.last.authenticate('new password')).to be_truthy
        end
      end

      context 'when token is invalid' do
        it 'returns a fail messsage' do
          post '/taskers/reset-password', token: 'invalid token', password: 'new password'
          expect(body['message']).to eq 'Token inválido.'
        end
      end
    end
  end
end
