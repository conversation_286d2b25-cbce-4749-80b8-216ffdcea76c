# frozen_string_literal: true

require 'model_helper'

RSpec.describe TaskerHistory, type: :model do
  it { is_expected.to be_a Mongoid::Document }
  it { is_expected.to be_a Mongoid::Timestamps }

  it { is_expected.to be_embedded_in :tasker }

  it { is_expected.to have_field(:completed_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:completed_preferential_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:completed_single_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:penalty).of_type(Float).with_default_value_of(0.0) }

  it { is_expected.to have_field(:partial_completed_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:partial_completed_preferential_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:partial_completed_single_jobs).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:partial_fidelity_points).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:last_job_discount_at).of_type(DateTime) }

  it { is_expected.to have_field(:partial_penalty).of_type(Float).with_default_value_of(0.0) }
  it { is_expected.to have_field(:last_penalty_discount_at).of_type(DateTime) }

  it { is_expected.to have_field(:background_check_last_result).of_type(String) }
  it { is_expected.to have_field(:background_check_last_score).of_type(Integer) }
  it { is_expected.to have_field(:background_check_status).of_type(String).with_default_value_of('unchecked') }
  it { is_expected.to have_field(:background_checked_at).of_type(DateTime) }

  it { is_expected.to have_field(:photo_identity_checked_at).of_type(DateTime) }
  it { is_expected.to have_field(:photo_identity_transaction_id).of_type(String) }
  it { is_expected.to have_field(:photo_identity_transaction_created_at).of_type(DateTime) }
  it { is_expected.to have_field(:photo_identity_score).of_type(Float) }
  it { is_expected.to have_field(:photo_identity_score_received_at).of_type(DateTime) }
  it { is_expected.to have_field(:photo_identity_attempts).of_type(Integer).with_default_value_of(0) }
  it { is_expected.to have_field(:photo_identity_state).of_type(String).with_default_value_of('unprocessed') }

  describe '#photo_identity_check_state' do
    subject { build :tasker_history }

    it { is_expected.to have_attributes photo_identity_check_state: 'unchecked' }

    context 'when photo identity is checked' do
      subject { build :tasker_history, photo_identity_checked_at: 1.day.ago }

      it { is_expected.to have_attributes photo_identity_check_state: 'checked' }
    end
  end

  describe '#photo_identity_score_received?' do
    subject { build :tasker_history, photo_identity_score_received_at: nil }

    it { is_expected.to have_attributes photo_identity_score_received?: false }

    context 'when photo identity is checked' do
      subject { build :tasker_history, photo_identity_score_received_at: Time.current }

      it { is_expected.to have_attributes photo_identity_score_received?: true }
    end
  end

  describe 'photo_identity_attempts increment' do
    let(:tasker) { create :tasker }
    let(:tasker_history) { tasker.history }

    it 'increments when photo_identity_score_received_at changes from nil to a value' do
      expect { tasker_history.update!(photo_identity_score_received_at: Time.current) }
        .to change(tasker_history, :photo_identity_attempts).from(0).to(1)
    end

    it 'does not increment when photo_identity_score_received_at is updated with another value' do
      tasker_history.update!(photo_identity_score_received_at: 1.day.ago)

      expect { tasker_history.update!(photo_identity_score_received_at: Time.current) }
        .not_to change(tasker_history, :photo_identity_attempts)
    end

    it 'does not increment when other fields are updated' do
      expect { tasker_history.update!(photo_identity_score: 0.8) }
        .not_to change(tasker_history, :photo_identity_attempts)
    end
  end
end
