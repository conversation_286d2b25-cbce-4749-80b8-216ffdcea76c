# frozen_string_literal: true

require 'model_helper'

RSpec.describe OrderTasker, type: :model do
  let(:order) { create :order, :with_requester_preference_days }

  subject { described_class.new order: order }

  it { is_expected.to be_a Parafuzo::Core::IntegrationTracker }

  it { expect(subject.days).to be_empty }

  context 'validations' do
    it { is_expected.to validate_presence_of(:tasker_id) }

    # Validating the presence of tasker instead of tasker_id cause unexpected
    # errors when editing an order_tasker in Admin.
    it { is_expected.not_to validate_presence_of(:tasker) }

    context 'when order_tasker is with duplicated tasker' do
      subject(:last_order_tasker) { order.order_taskers.last }

      let(:order) do
        Order.create(subscription: true, order_taskers: build_list(:order_tasker, 2, tasker: create(:tasker)))
      end

      it { expect(order).not_to be_valid }
      it { expect(last_order_tasker).not_to be_valid }
      it { expect(last_order_tasker.errors.full_messages).to include 'Tasker Já existe um registro com esse valor' }
    end
  end

  describe '#confirm!' do
    let(:order_tasker) { order.order_taskers.first }

    context 'when order_tasker is not answered' do
      it { expect { order_tasker.confirm! }.to change(order_tasker, :confirmed?).from(false).to(true) }
    end

    context 'when order_tasker is answered' do
      before { order_tasker.reject! }

      it { expect { order_tasker.confirm! }.not_to change(order_tasker, :confirmed?) }
    end
  end

  describe '#reject!' do
    let(:order_tasker) { order.order_taskers.first }

    context 'when order_tasker is not answered' do
      let(:reject_reason) { 'Muito longe de casa.' }

      it { expect { order_tasker.reject! }.to change(order_tasker, :rejected?).from(false).to(true) }
      it { expect { order_tasker.reject!(reject_reason) }.to change(order_tasker, :rejected_reason).to(reject_reason) }
    end

    context 'when order_tasker is answered' do
      before { order_tasker.confirm! }

      it { expect { order_tasker.reject! }.not_to change(order_tasker, :rejected?) }
    end
  end

  describe '#match!' do
    subject(:order_tasker) { described_class.new order: order }

    it { expect { order_tasker.match! }.to change(order_tasker, :matched?).to(true) }
  end

  describe '#can_be_matched?' do
    subject(:order_tasker) { order.order_taskers.first }

    context 'when it has just been created' do
      it { expect(order_tasker).not_to be_can_be_matched }
    end

    context 'when it has been confirmed' do
      it { expect { order_tasker.confirm! }.not_to change(order_tasker, :can_be_matched?) }
    end

    context 'when not confirmed and a day is selected' do
      it do
        expect { order_tasker.requester_preference_days.first.select! }
          .to change(order_tasker, :can_be_matched?).to(true)
      end
    end
  end

  describe '#answered?' do
    subject { order_tasker.answered? }

    context 'when order_tasker is accepted' do
      let(:order_tasker) { build :order_tasker, confirmed_at: Time.current }

      it { is_expected.to be_truthy }
    end

    context 'when order_tasker is rejected' do
      let(:order_tasker) { build :order_tasker, rejected_at: Time.current }

      it { is_expected.to be_truthy }
    end

    context 'when order_tasker is not answered?' do
      let(:order_tasker) { build :order_tasker }

      it { is_expected.to be_falsey }
    end
  end

  describe '#expired?' do
    subject(:order_tasker_expired) { order.order_taskers.first.expired? }

    context 'when order_tasker is old and do not have response' do
      let(:order) do
        create :order, :cleaning_subscription, order_taskers: [build(:order_tasker, updated_at: 1.year.ago)]
      end

      it { expect(order_tasker_expired).to be_truthy }
    end

    context 'when order_tasker is recent' do
      let(:order) do
        create :order, :cleaning_subscription, order_taskers: [build(:order_tasker, updated_at: 1.day.ago)]
      end

      it { expect(order_tasker_expired).to be_falsey }
    end

    context 'when order_tasker do not have updated_at' do
      let(:order) { create :order, :cleaning_subscription, order_taskers: [build(:order_tasker, updated_at: nil)] }

      it { expect(order_tasker_expired).to be_falsey }
    end

    context 'when order_tasker have been confirmed or rejected' do
      let(:order) do
        create :order, :cleaning_subscription, order_taskers: [build(:order_tasker, :confirmed, updated_at: 1.year.ago)]
      end

      it { expect(order_tasker_expired).to be_falsey }
    end
  end

  describe '#state' do
    subject { order.order_taskers.first.state }

    let(:order) { create :order, :cleaning_subscription, order_taskers: [order_tasker] }
    let(:order_tasker) { build :order_tasker }

    it { is_expected.to eq 'pending' }

    context 'when it is matched' do
      let(:order_tasker) { build :order_tasker, :matched }

      it { is_expected.to eq 'confirmed' }

      context 'when it is confirmed too' do
        let(:order_tasker) { build :order_tasker, :matched, :confirmed }

        it { is_expected.to eq 'confirmed' }

        context 'when it is rejected too' do
          let(:order_tasker) { build :order_tasker, :matched, :confirmed, :rejected }

          it { is_expected.to eq 'rejected' }
        end
      end
    end

    context 'when it is confirmed' do
      let(:order_tasker) { build :order_tasker, :confirmed }

      it { is_expected.to eq 'confirmed' }
    end

    context 'when it is rejected' do
      let(:order_tasker) { build :order_tasker, :rejected }

      it { is_expected.to eq 'rejected' }
    end
  end
end
