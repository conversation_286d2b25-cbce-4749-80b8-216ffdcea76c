# frozen_string_literal: true

require 'model_helper'

RSpec.describe Tasker, type: :model do
  subject(:tasker) { described_class.new }

  it { is_expected.to have_field(:political_exposure).of_type(String) }
  it { is_expected.to have_field(:political_exposure_updated_at).of_type(DateTime) }
  it { is_expected.to have_field(:current_sign_in_at).of_type(Time) }
  it { is_expected.to have_field(:last_sign_in_at).of_type(Time) }
  it { is_expected.to have_field(:current_sign_in_ip).of_type(String) }
  it { is_expected.to have_field(:last_sign_in_ip).of_type(String) }

  it { is_expected.to have_many(:tasker_feedbacks).with_foreign_key(:author_id) }
  it { is_expected.to have_many(:processed_feedbacks) }

  it { is_expected.to have_field(:category).of_type(String).with_default_value_of('uncategorized') }

  it { is_expected.to embed_one :address }
  it { is_expected.to embed_one(:company).with_autobuild }
  it { is_expected.to embed_one :history }
  it { is_expected.to embed_many :phones }
  it { is_expected.to embed_many :banks }
  it { is_expected.to embed_many :photos }
  it { is_expected.to embed_many :flags }

  it { is_expected.to have_index_for(state: 1, 'company.state': 1).with_options(background: true) }
  it { is_expected.to have_index_for('history.photo_identity_transaction_id': 1).with_options(background: true) }

  describe 'validates presence by state' do
    subject { build :tasker, state: }

    context 'when state is lead' do
      let(:state) { 'lead' }

      it { is_expected.not_to validate_presence_of(:birthdate) }
      it { is_expected.not_to validate_presence_of(:interest_address) }
      it { is_expected.not_to validate_presence_of(:latitude) }
      it { is_expected.not_to validate_presence_of(:longitude) }
    end

    context 'when state is disabled' do
      let(:state) { 'disabled' }

      it { is_expected.not_to validate_presence_of(:birthdate) }
      it { is_expected.not_to validate_presence_of(:interest_address) }
      it { is_expected.not_to validate_presence_of(:latitude) }
      it { is_expected.not_to validate_presence_of(:longitude) }
    end

    context 'when state is suspended' do
      let(:state) { 'suspended' }

      it { is_expected.to validate_presence_of(:birthdate) }
      it { is_expected.to validate_presence_of(:interest_address) }
      it { is_expected.to validate_presence_of(:latitude) }
      it { is_expected.to validate_presence_of(:longitude) }
    end

    context 'when state is onboarding' do
      let(:state) { 'onboarding' }

      it { is_expected.to validate_presence_of(:birthdate) }
      it { is_expected.to validate_presence_of(:interest_address) }
      it { is_expected.to validate_presence_of(:latitude) }
      it { is_expected.to validate_presence_of(:longitude) }
    end

    context 'when state is enabled' do
      let(:state) { 'enabled' }

      it { is_expected.to validate_presence_of(:birthdate) }
      it { is_expected.to validate_presence_of(:interest_address) }
      it { is_expected.to validate_presence_of(:latitude) }
      it { is_expected.to validate_presence_of(:longitude) }
    end
  end

  context 'with unformatted CPF' do
    subject(:tasker) { build :tasker, cpf: '22187509370' }

    it { expect { tasker.save }.to change(tasker, :cpf).to '221.875.093-70' }

    context 'when CPF is nil' do
      subject(:tasker) { build :tasker, cpf: nil }

      it { expect { tasker.save }.to change(tasker, :cpf).to('') }
    end
  end

  it 'notify_type disabled as default' do
    expect(subject.notify_type).to eq :disabled
  end

  it 'allows phone destroy' do
    model = create :tasker, phones: [build(:phone), build(:phone)]
    model.update phones_attributes: { '0' => { 'id' => model.phones.first.id, '_destroy' => '1' } }
    model.save

    expect(model.reload.phones.size).to eq 1
  end

  describe '#zones' do
    it { expect(described_class.new.zones).to be_empty }
  end

  describe '#gender' do
    it { expect(described_class.new(gender: 'm')).to be_male }
    it { expect(described_class.new(gender: 'f')).to be_female }

    context 'when has no gender set' do
      it { expect(described_class.new(gender: nil)).not_to be_male }
      it { expect(described_class.new(gender: nil)).not_to be_female }
    end

    context 'scoping by gender' do
      before do
        create :tasker, name: 'Rose',  gender: 'f'
        create :tasker, name: 'Bete',  gender: 'f'
        create :tasker, name: 'Carlo', gender: 'm'
      end

      it { expect(described_class.male.map(&:name)).to   match_array(['Carlo']) }
      it { expect(described_class.female.map(&:name)).to match_array(['Bete', 'Rose']) }
    end
  end

  describe '#metadata' do
    let(:metadata) { { iugu: { account_id: 'xxx' } } }
    let(:tasker) { create(:tasker, metadata: metadata) }

    it 'accesses metadata as hash' do
      expect(tasker.metadata[:iugu][:account_id]).to eq 'xxx'
    end

    it 'sets if hash' do
      tasker.metadata[:iugu][:account_id] = 'yyy'
      expect(tasker.metadata[:iugu][:account_id]).to eq 'yyy'
    end
  end

  describe '#geocode' do
    subject(:tasker) do
      create :tasker,
             name: 'João Silva',
             interest_address: 'Rua Euclides Pacheco, 1035, Vila Gomes Cardim, São Paulo, SP',
             address_attributes: {
               address: 'Rua Euclides Pacheco',
               number: 1035,
               complement: 'ap 81',
               neighborhood: 'Vila Gomes Cardim',
               city: 'São Paulo',
               state: 'SP'
             }
    end

    it 'sets coordinates based on interest address' do
      expect { tasker.geocode }.to change(tasker, :coordinates).to([-46.564585, -23.5472579])
    end
  end

  describe '#full_text_search' do
    before do
      create :enabled_tasker, name: 'John Due', zones: ['Norte']
      create :lead_tasker, name: 'Maggie Due'
      create :onboarding_tasker, name: 'Bart Due'
      create :disabled_tasker, name: 'Martin Due', zones: ['Norte']
      create :enabled_tasker, :with_all_services, name: 'Alfred Due', zones: %w[Sul Oeste Leste]
    end

    it 'searches by name' do
      expect(described_class.full_text_search('John')).to include(have_attributes(name: 'John Due'))
    end

    it 'searches by service name' do
      expect(described_class.full_text_search('passadoria')).to include(have_attributes(name: 'Alfred Due'))
    end

    it 'searches by zone' do
      expect(described_class.full_text_search('norte')).to include(
        have_attributes(name: 'John Due'),
        have_attributes(name: 'Martin Due')
      )
    end

    describe 'searches by document' do
      let(:tasker) { create :enabled_tasker, name: 'John Due' }

      it 'searches by CPF' do
        expect(described_class.full_text_search(tasker.cpf)).to include(have_attributes(name: 'John Due'))
      end

      it 'searches by RG' do
        expect(described_class.full_text_search(tasker.rg)).to include(have_attributes(name: 'John Due'))
      end
    end

    it 'searches by "Ativo"' do
      expect(described_class.full_text_search('ativo')).to include(
        have_attributes(name: 'John Due'),
        have_attributes(name: 'Alfred Due')
      )
    end

    it 'searches by "Enabled"' do
      expect(described_class.full_text_search('enabled')).to include(
        have_attributes(name: 'John Due'),
        have_attributes(name: 'Alfred Due')
      )
    end

    it 'searches by "Lead"' do
      expect(described_class.full_text_search('lead')).to include(
        have_attributes(name: 'Maggie Due')
      )
    end

    it 'searches by "Onboarding"' do
      expect(described_class.full_text_search('onboarding')).to include(
        have_attributes(name: 'Bart Due')
      )
    end

    it 'searches by "Disabled"' do
      expect(described_class.full_text_search('disabled')).to include(
        have_attributes(name: 'Martin Due')
      )
    end

    it 'searches by "Inativo"' do
      expect(described_class.full_text_search('Inativo')).to include(
        have_attributes(name: 'Martin Due')
      )
    end
  end

  describe '#last_job' do
    let(:tasker) { create :tasker }
    let(:job_taskers) { [build(:job_tasker, tasker: tasker)] }

    before do
      create(:job, id: '2 days ago', job_taskers: job_taskers, date: 2.days.ago)
      create(:job, id: '3 days ago', job_taskers: job_taskers, date: 3.days.ago)
    end

    it 'returns the correct job' do
      expect(tasker.last_job.id).to eq '2 days ago'
    end

    context 'when there is a cancelled job' do
      before do
        create(:job, state: :cancelled, id: '1 day ago', job_taskers: job_taskers, date: 1.day.ago)
      end

      it 'returns the correct job' do
        expect(tasker.last_job.id).to eq '2 days ago'
      end
    end
  end

  describe '#preferential_orders' do
    let(:tasker) { create :tasker }
    let!(:confirmed_order) { create :order, :with_one_confirmed_order_tasker, tasker: tasker, state: :approved }
    let!(:unconfirmed_order) { create :order, :with_one_order_tasker, tasker: tasker, state: :approved }
    let!(:rejected_order) { create :order, :with_one_rejected_order_tasker, tasker: tasker, state: :approved }
    let!(:paused_order) { create :order, :with_one_confirmed_order_tasker, tasker: tasker, state: :paused }
    let!(:order_other_tasker) { create :order, :with_one_confirmed_order_tasker, state: :approved }
    let!(:order_with_multiple_order_tasker) do
      create :order, :cleaning_subscription,
             state: :approved,
             order_taskers: [build(:order_tasker), build(:order_tasker, tasker: tasker, confirmed_at: Time.current)]
    end
    let!(:order_single_with_order_tasker) do
      create :order, :cleaning,
             state: :approved,
             order_taskers: [build(:order_tasker, tasker:, confirmed_at: Time.current)]
    end

    it { expect(tasker.preferential_orders.to_a).to include(confirmed_order) }
    it { expect(tasker.preferential_orders.to_a).to include(order_with_multiple_order_tasker) }
    it { expect(tasker.preferential_orders.to_a).not_to include(unconfirmed_order) }
    it { expect(tasker.preferential_orders.to_a).not_to include(rejected_order) }
    it { expect(tasker.preferential_orders.to_a).not_to include(paused_order) }
    it { expect(tasker.preferential_orders.to_a).not_to include(order_other_tasker) }
    it { expect(tasker.preferential_orders.to_a).not_to include(order_single_with_order_tasker) }
  end

  describe '#photo' do
    let(:photo1) { build :photo, image_url: 'image1.jpg' }
    let(:photo2) { build :photo, image_url: 'image2.jpg' }
    let(:tasker) { create :tasker, photos: [photo1, photo2] }

    it 'returns the last photo' do
      expect(tasker.photo).to eq photo2
    end

    context 'when there is no photo' do
      let(:tasker) { create :tasker }

      it 'returns a default photo' do
        expect(tasker.photo).to be_a PhotoDefault
      end

      it 'initializes default photo with model name' do
        expect(tasker.photo.model).to eq :tasker
      end
    end
  end

  describe '#phone' do
    it 'returns the main number' do
      tasker = create(:tasker, phones: [
        build(:phone, number: '(11) *********'),
        build(:phone, number: '(19) 997654321', main: true)
      ])

      expect(tasker.phone.number).to eq '(19) 997654321'
    end

    context 'when there is no main phone' do
      context 'and there is only cell phone numbers'
        it 'returns the first' do
          tasker = create(:tasker, phones: [
            build(:phone, number: '(11) *********'),
            build(:phone, number: '(19) 997654321')
          ])

          expect(tasker.phone.number).to eq '(11) *********'
        end
      end

      context 'and the first number is land line' do
        it 'returns the first cell phone number' do
          tasker = create(:tasker, phones: [
            build(:phone, number: '(15) ********'),
            build(:phone, number: '(11) *********')
          ])

          expect(tasker.phone.number).to eq '(11) *********'
        end
      end


      context 'when main number is land line' do
        it 'returns the first cell phone number' do
          tasker = create(:tasker, phones: [
            build(:phone, number: '(15) ********', main: true),
            build(:phone, number: '(11) *********')
          ])

          expect(tasker.phone.number).to eq '(11) *********'
        end
      end
    end

  describe '#bank' do
    let(:bank)   { build(:bank, main: true) }
    let(:tasker) { create(:tasker, banks: [bank]) }

    it { expect(tasker.bank).to eq bank }
  end

  describe '#active_name' do
    context 'active is true' do
      let(:tasker) { create :tasker, active: true }

      it 'is Ativo' do
        expect(tasker.active_name).to eq 'Ativo'
      end
    end

    context 'active is false' do
      let(:tasker) { create :tasker, state: 'lead' }

      it 'is Descansando' do
        expect(tasker.active_name).to eq 'Descansando'
      end
    end
  end

  describe '#authenticate' do
    let(:user) { User.create email: '<EMAIL>', password: 'password' }

    it 'returns true if password is correct' do
      expect(user.authenticate('password')).to be_truthy
    end

    it 'returns false if password is not correct' do
      expect(user.authenticate('worng password')).to be_falsey
    end

    it 'returns false when password is nil' do
      expect(user.authenticate(nil)).to be_falsey
    end
  end

  describe '#password=' do
    it 'is not persisted as plain text' do
      user = User.create email: '<EMAIL>', password: 'password'
      expect(user.password).not_to eql 'password'
    end

    it 'removes recovery token' do
      user = User.create email: '<EMAIL>', password: 'password', password_recovery: 'token'
      user.password = 'new password'
      expect(user.password_recovery).to be_nil
    end
  end

  describe 'forget_password!' do
    it 'sets a new token' do
      subject.forget_password!
      expect(subject.password_recovery).not_to be_nil
    end
  end

  describe '#admin_notes' do
    let(:tasker) { create :tasker }
    let(:admin_user) { create :admin_user, name: 'Juca' }

    before do
      AdminNote.create note: 'Esse job é loco', admin_user: admin_user, resource_id: tasker.id
    end

    it 'persists admin notes' do
      expect(tasker.admin_notes.count).to eq(1)
    end

    it 'persists note' do
      expect(tasker.admin_notes.first.note).to eq 'Esse job é loco'
    end

    it 'persists the admin user on note' do
      expect(tasker.admin_notes.first.admin_user).to eq admin_user
    end
  end

  describe 'scopes' do
    describe '.authenticable' do
      subject { described_class.authenticable }

      let!(:lead_tasker) { create :lead_tasker }
      let!(:onboarding_tasker) { create :onboarding_tasker }
      let!(:enabled_tasker) { create :enabled_tasker }
      let!(:suspended_tasker) { create :suspended_tasker }
      let!(:disabled_tasker) { create :disabled_tasker }

      it { is_expected.not_to include lead_tasker }
      it { is_expected.to include onboarding_tasker }
      it { is_expected.to include enabled_tasker }
      it { is_expected.to include suspended_tasker }
      it { is_expected.not_to include disabled_tasker }
    end

    describe '.recently_working' do
      subject { described_class.recently_working }

      let(:tasker) { create :tasker }

      it { is_expected.not_to include tasker }

      context 'when tasker has worked recently' do
        let(:tasker) { create :tasker, history: build(:tasker_history, :with_several_jobs) }

        it { is_expected.to include tasker }
      end
    end
  end

  describe '#notify_push!' do
    it 'sets notify_type to push' do
      subject.notify_push!
      expect(subject.notify_type).to eq :push
    end
  end

  describe '#notify_disabled!' do
    let(:tasker) { create :tasker, notify_type: :push }

    it 'sets notify_type to sms' do
      tasker.notify_disabled!
      expect(tasker.notify_type).to eq :disabled
    end
  end

  describe '#makes_service?' do
    let(:tasker) { build(:tasker) }

    it 'when service is included on list' do
      expect(tasker.makes_service?(:cleaning)).to be_truthy
    end

    it 'when service is not included on list' do
      expect(tasker.makes_service?(:ironing)).to be_falsey
    end
  end

  describe '#can_make_cash_jobs?' do
    subject { tasker.can_make_cash_jobs? }

    context 'when tasker is payable_by_cash and enabled' do
      let(:tasker) { create :tasker, :payable_by_cash, :enabled }

      it { is_expected.to be_truthy }
    end

    context 'when tasker is not payable_by_cash' do
      let(:tasker) { create :tasker, :enabled }

      it { is_expected.to be_falsey }
    end

    context 'when tasker is suspended' do
      let(:tasker) { create :suspended_tasker, :payable_by_cash }

      it { is_expected.to be_falsey }
    end

    context 'when tasker is disabled' do
      let(:tasker) { create :disabled_tasker, :payable_by_cash }

      it { is_expected.to be_falsey }
    end
  end

  describe '#main_device' do
    context 'when has an active device' do
      let(:tasker) { create(:tasker, :with_logged_device) }

      it { expect(tasker.main_device).not_to be_nil }
    end

    context 'when has no active device' do
      let(:tasker) { create(:tasker, :with_unlogged_device) }

      it { expect(tasker.main_device).to be_nil }
    end
  end

  describe '#device_login!' do
    let(:tasker)        { create :tasker }
    let(:device_params) { { uuid: 'uuid', app_version: '3.2.1' } }

    context "on tasker's first login" do
      subject { tasker.device_login!(device_params) }

      it { expect { subject }.to change { tasker.main_device }.from(nil) }
      it { expect { subject }.to change { tasker.devices.count }.from(0).to(1) }

      it { expect { subject }.to change { tasker.main_device.try(:uuid) }.from(nil).to('uuid') }
    end

    context 'when tasker already has an active device and log in to an entirely new device' do
      let!(:tasker)        { create(:tasker, :with_logged_device) }
      let!(:logged_device) { tasker.main_device }

      subject { tasker.device_login!(device_params) }

      it { expect { subject }.to change { tasker.main_device }.from(logged_device) }
      it { expect { subject }.to change { tasker.devices.count }.from(1).to(2) }

      it { expect { subject }.to change { logged_device.reload.active }.from(true).to(false) }

      it { expect { subject }.to change { tasker.main_device.uuid }.from(logged_device.uuid).to('uuid') }
    end

    context 'when tasker switch between devices' do
      let!(:tasker)     { create(:tasker, :with_multiple_devices) }

      let!(:old_device) { tasker.devices.find_by(active: true) }
      let!(:new_device) { tasker.devices.find_by(active: false) }

      subject { tasker.device_login!(device_params.merge(uuid: new_device.uuid)) }

      it { expect { subject }.not_to change { tasker.devices.count }.from(2) }
      it { expect { subject }.to     change { tasker.main_device }.from(old_device).to(new_device) }
      it { expect { subject }.to     change { tasker.main_device.uuid }.from(old_device.uuid).to(new_device.uuid) }

      it { expect { subject }.to     change { old_device.reload.active }.from(true).to(false) }
      it { expect { subject }.to     change { new_device.reload.active }.from(false).to(true) }
    end
  end

  describe 'score_for' do
    let(:tasker)  { create :tasker, scores: scores }
    let(:scores)  { { cleaning: { score: 5.0, total: 0 } } }

    context 'when service is in scores' do
      let(:service) { :cleaning }

      it { expect(tasker.score_for(service)).to eq 5.0 }
    end

    context "when service isn't in scores" do
      let(:service) { :express_cleaning }

      it { expect(tasker.score_for(service)).to eq 0.0 }
    end
  end

  describe 'score_total_for' do
    let(:tasker)  { create :tasker, scores: scores }
    let(:scores)  { { cleaning: { score: 5.0, total: 10 } } }

    context 'when service is in scores' do
      let(:service) { :cleaning }

      it { expect(tasker.score_total_for(service)).to eq 10 }
    end

    context "when service isn't in scores" do
      let(:service) { :express_cleaning }

      it { expect(tasker.score_total_for(service)).to eq 0 }
    end
  end

  describe 'score_total_for' do
    let(:tasker)  { create :tasker, scores: scores }
    let(:scores)  { { cleaning: { turbo_score_counter: 5 } } }

    context 'when service is in scores' do
      let(:service) { :cleaning }

      it { expect(tasker.turbo_score_counter_for(service)).to eq 5 }
    end

    context "when service isn't in scores" do
      let(:service) { :express_cleaning }

      it { expect(tasker.turbo_score_counter_for(service)).to eq 0 }
    end
  end

  describe 'generate_new_password' do
    let(:tasker)       { create :tasker }
    let(:new_password) { 'nova123' }

    before { allow_any_instance_of(Tasker).to receive(:random_password).and_return new_password }

    it 'calls update_attributes with right parameters' do
      expect(subject).to receive(:update_attributes).with(password: new_password)

      subject.generate_new_password
    end

    it { expect(subject.generate_new_password).to eq new_password }
  end

  describe '#job_taskers' do
    subject(:job_taskers) { tasker.job_taskers_from(jobs: Job.all) }

    let(:tasker) { create :tasker }
    let!(:completed_job) { create :job, :cleaning, :with_one_job_tasker, tasker: tasker, state: 'completed' }
    let!(:paid_job) { create :job, :cleaning, :with_one_job_tasker, tasker: tasker, state: 'paid' }
    let!(:invoiced_job) { create :job, :cleaning, :with_one_job_tasker, tasker: tasker, state: 'invoiced' }

    before do
      create :job, :cleaning, job_taskers: [build(:job_tasker, tasker: tasker), build(:job_tasker)], state: 'cancelled'
      create :job, :cleaning, job_taskers: [build(:job_tasker), build(:job_tasker, tasker: tasker)], state: 'skipped'
      create :job, :cleaning, job_taskers: [build(:job_tasker)], state: 'completed'
    end

    it { expect(job_taskers.count).to eq 5 }

    context 'when there is a list of jobs to look into' do
      subject(:job_taskers) { tasker.job_taskers_from(jobs: [completed_job, paid_job, invoiced_job]) }

      it { expect(job_taskers.count).to eq 3 }
    end

    context 'when there is no job_tasker on the tasker' do
      subject(:job_taskers) { tasker.job_taskers_from(jobs: Job.linked_to(create(:tasker))) }

      it { is_expected.to be_empty }
    end
  end

  describe '#lower_category?' do
    subject { tasker.lower_category? }

    let(:tasker) { create :tasker, category: category }
    let(:category) { 'uncategorized' }

    it { is_expected.to be_truthy }

    context 'when the category is bronze' do
      let(:category) { 'bronze' }

      it { is_expected.to be_truthy }
    end

    context 'when the category is silver' do
      let(:category) { 'silver' }

      it { is_expected.to be_falsey }
    end

    context 'when the category is gold' do
      let(:category) { 'gold' }

      it { is_expected.to be_falsey }
    end

    context 'when there are no scores' do
      let(:category) { nil }

      it { is_expected.to be_truthy }
    end
  end

  describe '#furniture_assembler?' do
    subject { tasker.furniture_assembler? }

    let(:tasker) { create :tasker, services: [create(:service, :furniture_assembly)] }

    it { is_expected.to be_truthy }

    context 'when the tasker does not have the furniture assembly service' do
      let(:tasker) { create :tasker, services: [create(:service, :cleaning)] }

      it { is_expected.to be_falsey }
    end

    context 'when the tasker have one more service besides furniture assembly' do
      let(:tasker) { create :tasker, services: [create(:service, :cleaning), create(:service, :furniture_assembly)] }

      it { is_expected.to be_falsey }
    end
  end

  describe '#receives_preferential_offers?' do
    subject { tasker.receives_preferential_offers? }

    context 'when tasker receives preferential offers' do
      let(:tasker) do
        create :tasker, flags: [build(:tasker_flag, name: 'receive_preferential_offers', allowed: true, accepted: true)]
      end

      it { is_expected.to be_truthy }
    end

    context 'when the tasker does not have permission to receives preferential offers' do
      let(:tasker) do
        create :tasker, flags: [
          build(:tasker_flag, name: 'receive_preferential_offers', allowed: false, accepted: true)
        ]
      end

      it { is_expected.to be_falsey }
    end

    context 'when the tasker does not want to receive preferential offers' do
      let(:tasker) do
        create :tasker, flags: [
          build(:tasker_flag, name: 'receive_preferential_offers', allowed: true, accepted: false)
        ]
      end

      it { is_expected.to be_falsey }
    end

    context 'when the tasker has no flags' do
      let(:tasker) { create :tasker }

      it { is_expected.to be_falsey }
    end
  end

  describe 'random_password' do
    let(:tasker) { create :tasker }

    before do
      allow_any_instance_of(Tasker).to receive(:rand).with(no_args).and_return 0.123
    end

    it 'returns a word that matchs with regex' do
      expect(tasker.send(:random_password)).to match('[a-z]*[0-9][0-9][0-9]')
    end
  end

  describe 'validates CPF document' do
    let(:tasker) { build(:tasker, cpf: '981.418.010-66') }

    it { expect(tasker).to be_valid }

    context 'when the number is not unique' do
      before do
        create(:tasker, cpf: '981.418.010-66')
      end

      it { expect(tasker).not_to be_valid }
    end

    context 'when the number is not valid' do
      let(:tasker) { build(:tasker, cpf: '000.000.000-00') }

      it { expect(tasker).not_to be_valid }
    end

    context 'when the number is not filled' do
      let(:tasker) { build(:tasker, cpf: '') }

      it { expect(tasker).not_to be_valid }
    end
  end

  describe '#processed_feedbacks_for' do
    subject { tasker.processed_feedbacks_for(service) }

    let(:tasker) { create :tasker, services: [create(:service, :cleaning)] }
    let(:service) { :cleaning }
    let!(:processed_feedback) { create :processed_feedback, tasker:, service: }

    it { is_expected.to include processed_feedback }

    context 'when the processed_feedback was for another service' do
      let(:processed_feedback) { create :processed_feedback, tasker:, service: :heavy_cleaning }

      it { is_expected.to be_empty }
    end

    context 'when the tasker does not make that service' do
      let(:service) { :heavy_cleaning }

      it { is_expected.to be_empty }
    end

    context 'when the service is a string' do
      let(:service) { 'heavy_cleaning' }

      it { is_expected.to be_empty }

      context 'when the service is a service the tasker does' do
        let(:service) { 'cleaning' }

        it { is_expected.to include processed_feedback }

        context 'when tasker is out of the rollout' do
          before { stub_const 'Tasker::TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS', 0.0 }

          it { is_expected.to be_empty }
        end
      end
    end

    describe '#active?' do
      let(:tasker) { create :tasker, state: }

      context 'when state is onboarding' do
        let(:state) { 'onboarding' }

        it { expect(tasker.active?).to be true }
      end

      context 'when state is enabled' do
        let(:state) { 'enabled' }

        it { expect(tasker.active?).to be true }
      end

      context 'when state is lead' do
        let(:state) { 'lead' }

        it { expect(tasker.active?).to be false }
      end

      context 'when state is suspended' do
        let(:state) { 'suspended' }

        it { expect(tasker.active?).to be false }
      end

      context 'when state is disabled' do
        let(:state) { 'disabled' }

        it { expect(tasker.active?).to be false }
      end
    end
  end

  describe '#processed_feedbacks_disabled?' do
    subject { tasker.processed_feedbacks_disabled? }

    let(:tasker) { create :tasker, id: BSON::ObjectId('669ffb74bbbcc10009da1ef0') }

    it { is_expected.to be_falsey }

    context 'when the rollout is 0%' do
      before { stub_const 'Tasker::TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS', 0.0 }

      it { is_expected.to be_truthy }
    end

    context 'when the rollout is 50%' do
      before { stub_const 'Tasker::TASKERS_PERCENTAGE_IN_PROCESSED_FEEDBACKS', 0.5 }

      it { is_expected.to be_truthy }

      context 'when the id is low on crc32' do
        let(:tasker) { create :tasker, id: BSON::ObjectId('669ffb76bbbcc10009da1ef1') }

        it { is_expected.to be_falsey }
      end
    end
  end

  describe '#fidelity_rate' do
    subject { tasker.fidelity_rate }

    let(:tasker) { create :tasker, history: build(:tasker_history, :with_bronze_fidelity) }

    it { is_expected.to eq 0.4 }

    context 'when the tasker is silver category' do
      let(:tasker) { create :tasker, history: build(:tasker_history, :with_silver_fidelity) }

      it { is_expected.to eq 0.53 }
    end

    context 'when the tasker is gold category' do
      let(:tasker) { create :tasker, history: build(:tasker_history, :with_gold_fidelity) }

      it { is_expected.to eq 0.7 }
    end

    context 'when the tasker has no completed jobs' do
      let(:tasker) { create :tasker }

      it { is_expected.to eq 0.0 }
    end
  end
end
