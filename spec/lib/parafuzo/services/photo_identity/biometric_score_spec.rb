# frozen_string_literal: true

require 'spec_helper'
require_relative '../../../../../lib/parafuzo/services/photo_identity/biometric_score'
require_relative '../../../../../lib/parafuzo/services/photo_identity/biometric_suspension'

RSpec.describe Services::PhotoIdentity::BiometricScore do
  subject(:processor) { described_class.new(transaction_id, score, received_at) }

  let(:transaction_id) { 'test_123' }
  let(:score) { '0.8' }
  let(:received_at) { '2025-07-29T12:51:00.5612611+00:00' }

  describe '#process' do
    context 'with invalid data' do
      context 'when transaction_id is nil' do
        let(:transaction_id) { nil }

        it 'returns true without processing' do
          expect(processor.process).to be true
        end
      end

      context 'when received_at is nil' do
        let(:received_at) { nil }

        it 'returns true without processing' do
          expect(processor.process).to be true
        end
      end

      context 'when received_at has invalid format' do
        let(:received_at) { 'invalid-date-format' }
        let(:tasker) { create :tasker, history: build(:tasker_history, photo_identity_transaction_id: transaction_id) }

        before { tasker }

        it 'returns true without processing' do
          expect(processor.process).to be true
        end
      end
    end

    context 'when tasker not found' do
      it 'returns true without processing' do
        expect(processor.process).to be true
      end
    end

    context 'with valid tasker' do
      let(:tasker) { create :tasker, history: build(:tasker_history, photo_identity_transaction_id: transaction_id) }

      before { tasker }

      context 'when updating score for first time' do
        it 'updates photo identity score' do
          expect { processor.process }
            .to change { tasker.reload.history.photo_identity_score }.to(0.8)
        end

        it 'updates received_at timestamp' do
          expect { processor.process }
            .to(change { tasker.reload.history.photo_identity_score_received_at })
        end

        it 'saves tasker' do
          expect { processor.process }
            .to(change { tasker.reload.updated_at })
        end
      end

      context 'when updating existing score with newer timestamp' do
        let(:tasker) do
          create :tasker,
                 history: build(:tasker_history,
                                photo_identity_transaction_id: transaction_id,
                                photo_identity_score: 0.5,
                                photo_identity_score_received_at: Time.parse('2025-07-29T10:00:00.0000000+00:00').utc)
        end

        it 'updates photo identity score' do
          expect { processor.process }
            .to change { tasker.reload.history.photo_identity_score }.from(0.5).to(0.8)
        end

        it 'updates received_at timestamp' do
          expect { processor.process }
            .to(change { tasker.reload.history.photo_identity_score_received_at })
        end
      end

      context 'when score triggers suspension' do
        let(:score) { '0.6' }

        before do
          create :admin_user, email: '<EMAIL>'
          allow(Parafuzo::Core::Queue).to receive(:publish)
        end

        it 'calls BiometricSuspension' do
          allow(Services::PhotoIdentity::BiometricSuspension).to receive(:new).with(tasker).and_call_original

          processor.process

          expect(Services::PhotoIdentity::BiometricSuspension).to have_received(:new).with(tasker)
        end

        it 'suspends tasker' do
          expect { processor.process }
            .to change { tasker.reload.state }.to('suspended')
        end
      end

      context 'when score is nil' do
        let(:score) { nil }

        before do
          create :admin_user, email: '<EMAIL>'
          allow(Parafuzo::Core::Queue).to receive(:publish)
        end

        it 'suspends tasker' do
          expect { processor.process }
            .to change { tasker.reload.state }.to('suspended')
        end
      end

      context 'when score does not trigger suspension' do
        let(:score) { '0.8' }

        it 'saves tasker' do
          expect { processor.process }.to(change { tasker.reload.updated_at })
        end

        it 'does not change tasker state' do
          expect { processor.process }.not_to(change { tasker.reload.state })
        end
      end

      context 'when tasker already suspended' do
        let(:tasker) do
          create :tasker, :suspended,
                 history: build(:tasker_history,
                                photo_identity_transaction_id: transaction_id)
        end
        let(:score) { '0.5' }

        it 'does not trigger suspension again' do
          allow(Services::PhotoIdentity::BiometricSuspension).to receive(:new)

          processor.process

          expect(Services::PhotoIdentity::BiometricSuspension).not_to have_received(:new)
        end
      end

      context 'when received_at is older than current' do
        let(:tasker) do
          create :tasker,
                 history: build(:tasker_history,
                                photo_identity_transaction_id: transaction_id,
                                photo_identity_score: 0.9,
                                photo_identity_score_received_at: Time.parse('2025-07-29T13:00:00.0000000+00:00').utc)
        end
        let(:received_at) { '2025-07-29T12:00:00.0000000+00:00' }

        it 'does not update score' do
          expect { processor.process }
            .not_to(change { tasker.reload.history.photo_identity_score })
        end
      end
    end
  end
end
