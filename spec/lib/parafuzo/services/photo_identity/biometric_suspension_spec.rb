# frozen_string_literal: true

require 'spec_helper'
require_relative '../../../../../lib/parafuzo/services/photo_identity/biometric_suspension'

RSpec.describe Services::PhotoIdentity::BiometricSuspension do
  subject(:service) { described_class.new(tasker) }

  let(:tasker) { create :tasker, :enabled }
  let!(:admin_user) { create :admin_user, email: '<EMAIL>' }

  before { allow(Parafuzo::Core::Queue).to receive(:publish) }

  describe '#suspend_with_note' do
    context 'when suspension succeeds' do
      it 'suspends the tasker' do
        expect { service.suspend_with_note }
          .to change { tasker.reload.state }.to('suspended')
      end

      it 'creates admin note' do
        expect { service.suspend_with_note }
          .to change(AdminNote, :count).by(1)
      end

      context 'when suspension is completed' do
        let(:created_note) { AdminNote.last }

        before { service.suspend_with_note }

        it 'creates note with biometric suspension code' do
          expect(created_note.note).to start_with('PrSuspBiometria001')
        end

        it 'includes biometric facial text in note' do
          expect(created_note.note).to include('biometria facial')
        end

        it 'includes channel reference in note' do
          expect(created_note.note).to include('#cs-atualização-cadastro-biometria')
        end

        it 'sets correct resource_id on note' do
          expect(created_note.resource_id).to eq(tasker.id.to_s)
        end

        it 'sets correct resource_class on note' do
          expect(created_note.resource_class).to eq('Tasker')
        end

        it 'associates note with admin user' do
          expect(created_note.admin_user).to eq(admin_user)
        end
      end
    end

    context 'when suspension fails' do
      before do
        tasker_service = instance_double(TaskerService, suspend: false)
        allow(TaskerService).to receive(:new).and_return(tasker_service)
      end

      it 'raises error' do
        expect { service.suspend_with_note }
          .to raise_error('Failed to suspend tasker')
      end

      it 'does not create admin note' do
        expect do
          service.suspend_with_note
        rescue StandardError
          nil
        end.not_to change(AdminNote, :count)
      end
    end
  end
end
