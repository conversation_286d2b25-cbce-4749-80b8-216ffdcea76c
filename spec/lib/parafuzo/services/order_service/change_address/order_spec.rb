# frozen_string_literal: true

require 'spec_helper'

describe OrderService::ChangeAddress::Order do
  subject(:change_address_order) { described_class.new(order, params, new_address).run }

  let(:order_date) { 2.days.from_now.beginning_of_day + 8.hours }
  let(:order) { create :order, :cleaning, :with_jobs, date: order_date, service_date: order_date }
  let(:new_address) { { cep: '80010-000', number: number, complement: 'A complement' } }
  let(:number) { rand(1000) }
  let(:params) { { requested_time: 3, bedroom_quantity: 6, bathroom_quantity: 4, property_type: 'house' } }

  context 'unit tests' do
    before do
      allow(OrderFactory).to receive(:new).and_return factory_service
      allow(OrderService).to receive(:new).and_return order_service
      allow(OrderService::AvailablePreferentialTaskers).to receive(:new).with(order:).and_return preferential_candidates
    end

    let(:factory_service) { instance_double(OrderFactory, update: true) }
    let(:order_service) do
      instance_double(OrderService, update_price: true, remove_preferential: true, update_preferential: true)
    end
    let(:preferential_candidates) { instance_double(OrderService::AvailablePreferentialTaskers, get: []) }

    it { expect { change_address_order }.to change { order.address.number }.from(order.address.number).to(number.to_s) }

    it 'calls order factory' do
      change_address_order

      expect(factory_service).to have_received(:update).with(save: false).once
    end

    it 'calls geocoder service' do
      geocoder = instance_double Workers::OrderGeocoder, run: true
      allow(Workers::OrderGeocoder).to receive(:new).with(order.id.to_s).and_return(geocoder)

      change_address_order

      expect(geocoder).to have_received(:run).with(force: true)
    end

    it 'calls order service' do
      change_address_order

      expect(order_service).to have_received(:update_price).with(errors: true).once
    end

    context 'with preferential tasker in preferential tasker candidates' do
      let(:preferential_candidates) { instance_double(OrderService::AvailablePreferentialTaskers, get: [tasker]) }
      let(:order) do
        create :order, :cleaning_subscription, :with_jobs, :with_one_order_tasker, date: order_date
      end
      let(:tasker) { order.order_taskers.first.tasker }

      it 'calls OrderService#remove_preferential' do
        change_address_order

        expect(order_service).to have_received(:remove_preferential).with(tasker, block_tasker: false).once
      end

      it 'calls OrderService#update_preferential' do
        change_address_order

        expect(order_service).to have_received(:update_preferential).with({ preferential_id: tasker.id.to_s }).once
      end
    end

    context 'with preferential match tasker in preferential tasker candidates' do
      let(:preferential_candidates) { instance_double(OrderService::AvailablePreferentialTaskers, get: [tasker]) }
      let(:order) { create :order, order_taskers:, date: order_date }
      let(:order_taskers) { build_list :order_tasker, 1, tasker:, requester_preference_days: }
      let(:tasker) { create :tasker }
      let(:requester_preference_days) do
        [build(:preference_day, wday: :monday, hour: '08:30'), build(:preference_day, wday: :tuesday, hour: '08:30')]
      end

      it 'calls OrderService#remove_preferential' do
        change_address_order

        expect(order_service).to have_received(:remove_preferential).with(tasker, block_tasker: false).once
      end

      it 'calls OrderService#update_preferential' do # rubocop:disable RSpec/ExampleLength
        change_address_order

        expect(order_service).to have_received(:update_preferential).with(
          {
            preferential_id: tasker.id.to_s,
            date: {
              '0' => { 'week_day' => :monday, 'hour' => '08:30' }, '1' => { 'week_day' => :tuesday, 'hour' => '08:30' }
            }
          }
        ).once
      end
    end

    context 'with preferential tasker not in preferential tasker candidates' do
      let(:order) do
        create :order, :cleaning_subscription, :with_jobs, :with_order_taskers, date: order_date
      end

      it 'calls OrderService#remove_preferential' do
        change_address_order

        expect(order_service).to have_received(:remove_preferential).exactly(5).times
      end

      it 'does not calls OrderService#update_preferential' do
        change_address_order

        expect(order_service).not_to have_received(:update_preferential)
      end
    end
  end

  context 'integration tests' do
    let(:number) { 609 }

    before { Timecop.freeze '2025-08-18 10:00' }

    after { Timecop.return }

    it 'changes order price' do
      VCR.use_cassette('change_order_address') do
        expect { change_address_order }.to change(order, :price)
      end
    end

    it 'changes order city address' do
      VCR.use_cassette('change_order_address') do
        expect { change_address_order }.to change { order.address.city }.from('São Paulo').to('Curitiba')
      end
    end

    context 'with one preferential tasker in preferential tasker candidates' do
      let(:tasker) { create :enabled_tasker, :with_flags, coordinates: [-1, -1] }
      let(:order) { create :order, :cleaning_subscription, :with_order_taskers, coordinates: [-1, -1] }

      before do
        order.order_taskers.first.update(tasker:)
        create :job, :completed_with_tasker, order:, tasker:, user: order.user, date: 1.day.ago
        create :job, :pending, :with_one_empty_job_tasker, order:, user: order.user
        create :stored_config, :sms
      end

      it 'removes order_taskers for taskers that are no longer eligible' do
        VCR.use_cassette('change_order_address_removing_order_taskers') do
          expect { change_address_order }.to change(order.order_taskers, :length).from(5).to(3)
        end
      end
    end

    context 'with preferential tasker not in preferential tasker candidates due to distance' do
      before do
        create(:job, :completed_with_tasker, order: order, user: order.user, tasker: order.order_taskers.first.tasker)
        create(:job, :pending, :with_one_empty_job_tasker, order: order, user: order.user)
        create(:stored_config, :sms)
      end

      let(:order) do
        create :order, :cleaning_subscription, :with_one_order_tasker, tasker: tasker, coordinates: [-1, -1]
      end
      let(:tasker) { create(:tasker, coordinates: [-0.8, -0.8]) }

      it 'removes all order_taskers' do
        VCR.use_cassette('change_order_address_removing_order_taskers') do
          expect { change_address_order }.to change(order.order_taskers, :length).from(1).to(0)
        end
      end
    end
  end
end
