# frozen_string_literal: true

module Api
  class Sendbird < Sinatra::Base
    module Webhooks
      class GroupChannel
        # Processor for Sendbird 'group_channel:user_joined' webhook.
        #
        class UserJoined
          attr_reader :params

          def initialize(params)
            @params = params
          end

          def process
            return [200, { message: 'Data was successfully skipped.' }.to_json] if job_id.blank? || job.blank?

            job.integrated_with!(:sendbird) unless job.integrated_with?(:sendbird)

            tasker_user? ? tasker_joined_chat : user_joined_chat

            [200, { message: 'Data was successfully updated.' }.to_json]
          end

          private

          def job_id
            @job_id ||= begin
              channel_url = params.dig('channel', 'channel_url')

              params.dig('channel', 'channel_url').sub(/^job_/, '') if channel_url&.starts_with?('job_')
            end
          end

          def job
            @job ||= ::Job.find job_id
          end

          def tasker_user?
            raw_id = params.dig('user', 'user_id')
            return false unless raw_id

            raw_id.starts_with?('tasker:')
          end

          def tasker_joined_chat
            job_tasker = job.job_taskers.find_by(tasker_id: user_id)
            return [404, 'Unable to find the job tasker.'] if job_tasker.blank?

            job_tasker.integrated_with! :sendbird, { tasker_id: user_id }
          end

          def user_joined_chat
            job.integrations[:sendbird] ||= {}
            job.integrations[:sendbird][:user_joined_at] ||= Time.current.iso8601
            job.save
          end

          # Removes `user:` or `tasker:` from the raw `user_id`.
          #
          def user_id
            params.dig('user', 'user_id').gsub(/^[a-z]+:/, '')
          end
        end
      end
    end
  end
end
