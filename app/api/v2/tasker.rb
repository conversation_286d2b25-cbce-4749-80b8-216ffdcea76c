# frozen_string_literal: true

require 'sinatra/json'
require 'sinatra/namespace'

require_relative '../../../lib/parafuzo/core/event_tracker'

module Api
  module V2
    # Taskers endpoints consumed by tasker-webapp.
    #
    class Tasker < Sinatra::Base
      register Sinatra::Namespace
      register Api::Services::Warden
      set :warden_scope, :tasker

      include Parafuzo::Core::EventTracker

      include ActionView::Helpers::NumberHelper

      namespace '/taskers' do
        before { authenticate_tasker }
        after { response.body = json(response.body) }

        namespace '/preferentials' do
          get do
            { data: current_user(:tasker).preferential_orders.map { preferential_customer_data(_1) } }
          end

          get '/:order_id' do
            order = find_tasker_preferencial_order(params[:order_id])

            halt(404) unless order

            { data: order_details(order) }
          end

          delete '/:order_id' do
            order = find_tasker_preferencial_order(params[:order_id])
            halt(404) unless order

            remove_preferential = OrderService.new(order).remove_preferential(current_user(:tasker))
            halt(422) unless remove_preferential

            TransactionalMailer.preferential_tasker_removed(order.id.to_s, current_user(:tasker).id.to_s).deliver

            event_tracker.info(
              event_tracker_payload(order), labels: { type: 'tasker.preferentials.delete' }
            )

            { data: { message: 'Preferential removed' } }
          end
        end

        namespace '/processed_feedbacks' do
          get '/:service' do
            tasker = current_user :tasker
            processed_feedbacks = tasker.processed_feedbacks_for(params[:service]).order(created_at: :desc)

            {
              data: {
                service: params[:service],
                score: number_with_precision(tasker.score_for(params[:service]), FormatHelper.format_float_params),
                processed_feedbacks: processed_feedbacks.map { |pf| { id: pf.id.to_s, review: pf.review } }
              }
            }
          end
        end

        # @note This payload is duplicated at {Decorators::Tasker::App}
        #
        namespace '/scores' do
          get do
            scores = []
            scores << current_user(:tasker).scores.slice(:punctuality, :friendliness).map do |service, infos|
              parsed_score(service, infos)
            end
            scores << services_scores

            { data: { scores: scores.flatten } }
          end
        end
      end

      private

      def parsed_score(service, infos)
        {
          service:,
          score: number_with_precision(infos[:score], FormatHelper.format_float_params), total:
            infos[:total]
        }
      end

      def services_scores
        tasker = current_user(:tasker)

        tasker.scores.except(:punctuality, :friendliness, :subscriptions, :laundry).map do |service, infos|
          parsed_score(service, infos).merge(
            processed_feedbacks: tasker.processed_feedbacks_for(service.to_sym).exists?
          )
        end
      end

      def event_tracker_payload(order)
        {
          message: "tasker.preferentials.delete on Order:#{order.id}",
          author: { tasker_id: current_user(:tasker).id.to_s },
          data: {
            order_id: order.id.to_s,
            user_id: order.user_id.to_s,
            tasker_id: current_user(:tasker).id.to_s
          }
        }
      end

      def authenticate_tasker
        authenticate scope: :tasker
        halt(403, { message: 'Not authorized' }) if current_user(:tasker).blank?
      end

      def preferential_customer_data(order)
        {
          user: { name: order.user.name },
          order: {
            id: order.id.to_s,
            frequency: order_frequency(order)
          }
        }
      end

      def taskers_per_job(order)
        JobFactory.new(order).taskers_per_job
      end

      def bonus_per_tasker(order)
        return 0.0 if order.service == :express_cleaning

        (order.payout / taskers_per_job(order) * BonusService::PREFERENTIAL_PERCENTAGE).round(2)
      end

      # Bonus payout calculation is duplicated with {BonusService::Job} and {OrderService::Preferential} temporarily.
      #
      def order_details(order)
        {
          user: { name: order.user.name },
          order: {
            frequency: order_frequency(order),
            job_template: job_template(order),
            address: order.address.as_json.slice(:cep, :address, :number, :complement, :neighborhood, :city, :state),
            service: order.service.to_s,
            payout_value: (order.payout / taskers_per_job(order)).round(2),
            bonus_value: bonus_per_tasker(order),
            next_job: next_scheduled_job(order)
          }
        }
      end

      def order_frequency(order)
        order[:parameters].find { _1['name'] == :subscription_type }&.dig('value', 'value')
      end

      def job_template(order)
        {
          week_days: order.requested_week_days,
          begin_at: order.service_time.strftime('%H:%M'),
          end_at: (order.service_time + order.duration.round(2).hours).strftime('%H:%M'),
          duration: order.duration.round(2)
        }
      end

      def next_job(order)
        @next_job ||= order.jobs.opened.where('job_taskers.tasker_id': current_user(:tasker).id).asc(:date).first
      end

      def next_job_duration(order)
        (next_job(order).work_time / taskers_per_job(order)).round(2)
      end

      def next_job_end_at(order)
        return unless next_job(order)

        (next_job(order).date + next_job_duration(order).hours).strftime('%H:%M') if next_job(order)
      end

      def next_scheduled_job(order)
        return unless next_job(order)

        {
          date: next_job(order).date.to_date.to_s,
          begin_at: next_job(order).date.strftime('%H:%M'),
          end_at: next_job_end_at(order),
          duration: next_job_duration(order)
        }
      end

      def find_tasker_preferencial_order(order_id)
        current_user(:tasker).preferential_orders.find(order_id)
      end
    end
  end
end
