# frozen_string_literal: true

require 'sinatra/contrib'
require 'json'

module Api
  class Tasker < Sinatra::Base
    register Sinatra::Contrib
    register Services::Decorator

    register Services::AppAuth

    register Services::ACL
    set :model, Tasker
    set :warden_scope, :tasker

    register Services::Json
    set :filter, TaskerParamsFilter

    use Api::TaskersOffers
    use Api::TaskersJobs
    use Api::Taskers::Confirmations

    respond_to :json, :appjson

    configure do
      mime_type :appjson, 'application/json+app'
    end

    LAST_FEEDBACKS_ANTECEDENCE = ENV.fetch('LAST_FEEDBACKS_ANTECEDENCE_IN_DAYS', 60).to_i.days

    namespace '/taskers' do
      get '/me' do
        authenticate scope: :tasker
        halt(401) unless current_user(:tasker)

        unless stealth_mode?
          tasker_app_version = request.env['HTTP_X_APP_VERSION']
          tasker.update notify_app_version: tasker_app_version if tasker_app_version
        end

        json decorate(current_user(:tasker))
      end

      def since
        Date.parse(params[:since])
      rescue TypeError, ArgumentError
        LAST_FEEDBACKS_ANTECEDENCE.ago.to_date
      end

      get '/last_feedbacks' do
        authenticate scope: :tasker
        halt(401) unless current_user(:tasker)

        since_job_dates = since - LAST_FEEDBACKS_ANTECEDENCE

        condition = {
          state: :reviewed,
          :updated_at.gt => since,
          :score => 5,
          :review.nin => [nil, ''],
          :tasker_id.eq => current_user(:tasker).id
        }

        scope = Feedback.where(condition.merge(user_id: nil))
                        .or(condition.merge(friendliness: 5))
                        .includes(:job)

        list = scope.delete_if do |f|
          next if f.job.blank?

          f.job.date < since_job_dates
        end

        json decorate(list)
      end

      get '/rollout' do
        authenticate scope: :tasker
        tasker = current_user(:tasker)
        halt(401) unless tasker

        json(::Taskers::Rollout.new(tasker).features)
      end

      get '/:id' do
        auth_control

        tasker = ::Tasker.find params[:id]
        halt 404 unless tasker

        json decorate(tasker)
      end

      patch '/:id' do
        auth_control

        tasker = ::Tasker.find params[:id]
        halt(404) unless tasker

        tasker.update model_params.except(:notify_token, :flags)

        model_params[:flags]&.each { tasker.flags.find_or_create_by(name: _1[:name]).update(accepted: _1[:accepted]) }

        unless stealth_mode?
          tasker.update_attributes model_params.merge(notify_app_version: current_app_version)
          tasker.notify_token.present? ? tasker.notify_push! : tasker.notify_disabled!

          tasker.main_device.update(notification_token: model_params[:notify_token]) if tasker.main_device
        end

        json decorate(tasker)
      end

      post '/login', block_multiple_devices: false do
        halt(404) unless ::Tasker.authenticable.find_by(cpf: params['cpf'])
        authenticate scope: :tasker
        tasker = current_user(:tasker)

        if request_from_app? && !stealth_mode?
          device_login!

          tasker.update last_sign_in_at: tasker.current_sign_in_at,
                        last_sign_in_ip: tasker.current_sign_in_ip,
                        current_sign_in_at: Time.current,
                        current_sign_in_ip: request.env['HTTP_X_FORWARDED_FOR']&.split(',')&.first.presence
        end

        json decorate(tasker)
      end

      delete '/logout' do
        authenticate scope: :tasker

        unless stealth_mode?
          TaskerService.new(current_user(:tasker)).logout
          logout
        end

        status 204
      end

      post '/forget-password' do
        protected!
        tasker = ::Tasker.authenticable.find_by cpf: params[:cpf]

        if tasker and tasker.email.present?
          TaskerService.new(tasker).reset_password!

          json success: true, message: 'Email enviado com sucesso!'
        else
          json success: false, message: 'CPF não cadastrado ou email inválido.'
        end
      end

      post '/reset-password' do
        protected!
        tasker = ::Tasker.find_by password_recovery: params[:token]

        if tasker
          tasker.update_attributes password: params[:password]

          json success: true, message: 'Senha alterada com sucesso!'
        else
          json success: false, message: 'Token inválido.'
        end
      end
    end
  end
end
