# frozen_string_literal: true

module Api
  module Services
    module Warden
      module Helpers
        def auth_control
          return true if authorized?

          authenticate scope: settings.warden_scope
        end

        def protected_and_authenticated?(*args)
          protected! && authenticated?(*args)
        end

        def warden
          request.env['warden']
        end

        def authenticated?(scope=nil)
          scope ? warden.authenticated?(scope) : warden.authenticated?
        end
        alias_method :logged_in?, :authenticated?

        def validate_auth(*args)
          warden.authenticate(*args)
        end

        def authenticate(*args)
          warden.authenticate!(*args)
        end
        alias_method :login, :authenticate

        def logout(scopes = nil)
          scopes ? warden.logout(scopes) : warden.logout(warden.config.default_scope)
        end

        def user(scope = nil)
          scope ? warden.user(scope) : warden.user
        end
        alias_method :current_user, :user

        def zeus_session_cookie
          rack_session = Parafuzo::RackSession.new(current_user, secret: AppConfig["session"]["zeus_secret"].to_s)

          { "rack.session" => rack_session.generate_cookie }
        end
      end

      # TODO Fix this sh*t - Não funciona em ambiente de teste :facepalm:
      #
      def self.rack_options
        if ENV["RACK_ENV"] == "test"
          { secret: "3c88f4d063eb6576a6dfa5e9b1b64b113d68dd17" }
        else
          { domain: AppConfig["session"]["domain"].to_s, secret: AppConfig["session"]["secret"].to_s }
        end
      end

      def self.registered(app)
        app.helpers Warden::Helpers
        app.register BasicAuth

        app.use Rack::Session::Cookie, rack_options

        app.use ::Warden::Manager do |manager|
          manager.serialize_into_session { |user| user.id }
          manager.serialize_from_session { |id| ::User.find(id) }
          manager.serialize_from_session(:tasker) { |id| ::Tasker.find(id) }

          manager.scope_defaults :default, strategies: %i[password facebook apple], action: 'auth/unauthenticated'
          manager.scope_defaults :tasker, strategies: [:cpf], action: 'auth/unauthenticated'

          manager.intercept_401 = false

          manager.failure_app = ApiApp
        end
      end
    end
  end
end
