# frozen_string_literal: true

require 'functions_framework'

FunctionsFramework.on_startup do
  require 'base64'
  require 'json'

  require_relative 'config/initializers/mongoid'
  require_relative 'config/initializers/redis'
  require_relative 'config/initializers/resque_client'
  require_relative 'config/initializers/api_url'
  require_relative 'lib/parafuzo/services/photo_identity/biometric_suspension'
  require_relative 'lib/parafuzo/services/photo_identity/biometric_score'
end

FunctionsFramework.cloud_event 'poseidon-photo_identity-score_updater' do |event|
  data = JSON.parse(Base64.decode64(event.data['message']['data']))
  transaction_id, score, received_at = data.values_at('transaction_id', 'score', 'received_at')

  Services::PhotoIdentity::BiometricScore.new(transaction_id, score, received_at).process
end
