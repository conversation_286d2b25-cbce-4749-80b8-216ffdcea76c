# For more information, run:
#
#    gcloud meta list-files-for-upload

# Ignore all files by default
*

# Exposes only used file
!.
!Gemfile*
!*.gemspec
!*.rb

# Exposes required config files
!config
config/*
!config/initializers
config/initializers/*
!config/mongoid.yml

# Exposes required lib files
!lib
lib/*
!lib/parafuzo
lib/parafuzo/*
!lib/parafuzo/core
lib/parafuzo/core/*
!lib/parafuzo/find_tasker
lib/parafuzo/find_tasker/*
!lib/parafuzo/helpers
lib/parafuzo/helpers/*
!lib/parafuzo/services
lib/parafuzo/services/*
!lib/parafuzo/services/taskers
lib/parafuzo/services/taskers/*
!lib/parafuzo/services/home_maintenance
lib/parafuzo/services/home_maintenance/*
!lib/parafuzo/services/photo_identity
lib/parafuzo/services/photo_identity/*
!lib/parafuzo/factories
lib/parafuzo/factories/*
!lib/parafuzo/models
lib/parafuzo/models/*
!lib/parafuzo/models/support
lib/parafuzo/models/support/*
!lib/parafuzo/models/enums
lib/parafuzo/models/enums/*
!lib/parafuzo/models/serializers
lib/parafuzo/models/serializers/*
!lib/parafuzo/models/serializers/notify_changes
lib/parafuzo/models/serializers/notify_changes/*
!lib/parafuzo/models/validators
lib/parafuzo/models/validators/*
!lib/parafuzo/models/validators/order_detail
lib/parafuzo/models/validators/order_detail/*
!lib/parafuzo/mongoid
lib/parafuzo/mongoid/*
!lib/parafuzo/mongoid/relations
lib/parafuzo/mongoid/relations/*
!lib/parafuzo/pricing
lib/parafuzo/pricing/*
!lib/parafuzo/pricing/strategies
lib/parafuzo/pricing/strategies/*
!lib/parafuzo/workers
lib/parafuzo/workers/*
!lib/parafuzo/workers/background_check
lib/parafuzo/workers/background_check/*

# Exposes ruby files discovered by using: $LOADED_FEATURES
!config/mongoid.yml
!config/initializers/api_url.rb
!config/initializers/mongoid.rb
!config/initializers/redis.rb
!config/initializers/resque_client.rb
!config/initializers/time_zone.rb
!lib/parafuzo/core/config.rb
!lib/parafuzo/core/integration_tracker.rb
!lib/parafuzo/core/job.rb
!lib/parafuzo/core/notify.rb
!lib/parafuzo/core/queue.rb
!lib/parafuzo/exception.rb
!lib/parafuzo/factories/job.rb
!lib/parafuzo/find_tasker/config.rb
!lib/parafuzo/helpers/boolean.rb
!lib/parafuzo/helpers/date_time.rb
!lib/parafuzo/helpers/phone_helper.rb
!lib/parafuzo/models/address.rb
!lib/parafuzo/models/admin_note.rb
!lib/parafuzo/models/admin_user.rb
!lib/parafuzo/models/bank.rb
!lib/parafuzo/models/company.rb
!lib/parafuzo/models/coupon.rb
!lib/parafuzo/models/coordinates.rb
!lib/parafuzo/models/credit_card.rb
!lib/parafuzo/models/deal.rb
!lib/parafuzo/models/deal_installment.rb
!lib/parafuzo/models/device.rb
!lib/parafuzo/models/entry.rb
!lib/parafuzo/models/enums/bank_code.rb
!lib/parafuzo/models/enums/detail_types.rb
!lib/parafuzo/models/enums/genders.rb
!lib/parafuzo/models/event.rb
!lib/parafuzo/models/feedback.rb
!lib/parafuzo/models/incident.rb
!lib/parafuzo/models/invoice.rb
!lib/parafuzo/models/job.rb
!lib/parafuzo/models/job_tasker.rb
!lib/parafuzo/models/offer.rb
!lib/parafuzo/models/order.rb
!lib/parafuzo/models/order_detail.rb
!lib/parafuzo/models/order_tasker.rb
!lib/parafuzo/models/payment.rb
!lib/parafuzo/models/phone.rb
!lib/parafuzo/models/photo.rb
!lib/parafuzo/models/photo_default.rb
!lib/parafuzo/models/preference_day.rb
!lib/parafuzo/models/price_group.rb
!lib/parafuzo/models/price_instruction.rb
!lib/parafuzo/models/processed_feedback.rb
!lib/parafuzo/models/service.rb
!lib/parafuzo/models/service_item.rb
!lib/parafuzo/models/service_param.rb
!lib/parafuzo/models/social_account.rb
!lib/parafuzo/models/support/authenticable.rb
!lib/parafuzo/models/support/criticable.rb
!lib/parafuzo/models/support/email_normalizable.rb
!lib/parafuzo/models/support/parametarizable.rb
!lib/parafuzo/models/support/payable.rb
!lib/parafuzo/models/support/payout_round.rb
!lib/parafuzo/models/support/photo_version.rb
!lib/parafuzo/models/tasker.rb
!lib/parafuzo/models/tasker_company.rb
!lib/parafuzo/models/tasker_feedback.rb
!lib/parafuzo/models/tasker_feedback_question.rb
!lib/parafuzo/models/tasker_flag.rb
!lib/parafuzo/models/tasker_history.rb
!lib/parafuzo/models/tasker_history_job.rb
!lib/parafuzo/models/user.rb
!lib/parafuzo/models/user_bonus_tasker.rb
!lib/parafuzo/models/serializers/notify_changes/job.rb
!lib/parafuzo/models/validators/checkout_order_validator.rb
!lib/parafuzo/models/validators/cpf_validator.rb
!lib/parafuzo/models/validators/job_overlap_validator.rb
!lib/parafuzo/models/validators/job_validator.rb
!lib/parafuzo/models/validators/order_detail/base.rb
!lib/parafuzo/models/validators/order_detail/boolean.rb
!lib/parafuzo/models/validators/order_detail/categorized_hour.rb
!lib/parafuzo/models/validators/order_detail/date.rb
!lib/parafuzo/models/validators/order_detail/date_time.rb
!lib/parafuzo/models/validators/order_detail/email.rb
!lib/parafuzo/models/validators/order_detail/float.rb
!lib/parafuzo/models/validators/order_detail/integer.rb
!lib/parafuzo/models/validators/order_detail/select.rb
!lib/parafuzo/models/validators/order_detail/select_one.rb
!lib/parafuzo/models/validators/order_detail/string.rb
!lib/parafuzo/models/validators/order_detail/time.rb
!lib/parafuzo/models/validators/order_detail_validator.rb
!lib/parafuzo/models/validators/order_validator.rb
!lib/parafuzo/models/validators/tasker_verify_validator.rb
!lib/parafuzo/mongoid/relations/embeds_many.rb
!lib/parafuzo/pricing/pricing.rb
!lib/parafuzo/pricing/strategies/count.rb
!lib/parafuzo/pricing/strategies/divide.rb
!lib/parafuzo/pricing/strategies/match.rb
!lib/parafuzo/pricing/strategies/maximum.rb
!lib/parafuzo/pricing/strategies/minimum.rb
!lib/parafuzo/pricing/strategies/multiply.rb
!lib/parafuzo/pricing/strategies/reduce.rb
!lib/parafuzo/pricing/strategies/round.rb
!lib/parafuzo/pricing/strategies/strategy.rb
!lib/parafuzo/pricing/strategies/sum.rb
!lib/parafuzo/services/home_maintenance/user_updater.rb
!lib/parafuzo/services/taskers/history.rb
!lib/parafuzo/services/photo.rb
!lib/parafuzo/services/tasker.rb
!lib/parafuzo/services/taskers/category.rb
!lib/parafuzo/services/taskers/services_manager.rb
!lib/parafuzo/services/photo_identity/biometric_suspension.rb
!lib/parafuzo/services/photo_identity/biometric_score.rb
!lib/parafuzo/workers/base.rb
!lib/parafuzo/workers/notify.rb
!lib/parafuzo/workers/background_check/add_taskers.rb
