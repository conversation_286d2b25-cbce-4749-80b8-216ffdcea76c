# frozen_string_literal: true

require 'sinatra/base'
require 'sinatra/cross_origin'
require 'sinatra/reloader'
require 'graphql'
require 'jwt'

module GraphqlApi
  autoload :Accounts, './app/graphql_api/accounts'
  autoload :Auth, './app/graphql_api/auth'
  autoload :Base, './app/graphql_api/base'
  autoload :Checkout, './app/graphql_api/checkout'
  autoload :Coupon, './app/graphql_api/coupon'
  autoload :Decorators, './app/graphql_api/decorators'
  autoload :Devices, './app/graphql_api/devices'
  autoload :Dw, './app/graphql_api/dw'
  autoload :Enums, './app/graphql_api/enums'
  autoload :Feedbacks, './app/graphql_api/feedbacks'
  autoload :HotSites, './app/graphql_api/hot_sites'
  autoload :Jobs, './app/graphql_api/jobs'
  autoload :Location, './app/graphql_api/location'
  autoload :MutationType, './app/graphql_api/mutation_type'
  autoload :Orders, './app/graphql_api/orders'
  autoload :QueryType, './app/graphql_api/query_type'
  autoload :Schema, './app/graphql_api/schema'
  autoload :Subscriptions, './app/graphql_api/subscriptions'
  autoload :Support, './app/graphql_api/support'
  autoload :Types, './app/graphql_api/types'
  autoload :Filters, './app/graphql_api/filters'

  class App < Sinatra::Base
    register Api::Services::Warden
    register Sinatra::CrossOrigin

    configure :development do
      register Sinatra::Reloader
    end

    configure do
      set :protection, except: [:json_csrf]
    end

    options '*' do
      response.headers['Allow'] = 'POST, OPTIONS'
      response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, Accept, cf-turnstile-response'
      response.headers['Access-Control-Allow-Origin'] = '*'
      200
    end

    get('/') { 'OK' }

    post('/') do
      cross_origin allow_origin: '*'

      # A solution to deal with Warden params in GraphQL resolves.
      #
      # @note Without this line an error happens while parsing request parameters at
      #  #GraphqlApi::Accounts::Mutations::Login, #GraphqlApi::Accounts::Mutations::AppleLogin and
      #  #GraphqlApi::Accounts::Mutations::FacebookLogin revolves. It seems like a Warden formatting problem after
      #  execute the schema.
      #
      request.env['warden'].params

      body = request.body.read
      halt 422 if body.blank?

      params = JSON.parse(body).with_indifferent_access

      json Schema.execute(
        params[:query],
        variables: params[:variables],
        context: {
          current_user_id: current_user_id(request),
          headers: {
            recaptcha_token: request.env['HTTP_CF_TURNSTILE_RESPONSE'],
            'HTTP_X_FORWARDED_FOR' => request.env['HTTP_X_FORWARDED_FOR']
          },
          warden: request.env['warden']
        }
      )
    end

    def current_user_id(request)
      return if request.env['HTTP_AUTHORIZATION'].blank?

      token = request.env['HTTP_AUTHORIZATION'].sub('Bearer ', '')
      secret = ENV.fetch 'JWT_SECRET'
      issuer = ENV.fetch 'JWT_ISSUER'

      begin
        decoded = JWT.decode token, secret, true, algorithm: 'HS512', iss: issuer
        decoded.first['sub'].sub('ParafuzoUser:', '')
      rescue StandardError
        nil
      end
    end
  end

  def self.result(messages, result, successful)
    { messages: messages, result: result, successful: successful }
  end
end
