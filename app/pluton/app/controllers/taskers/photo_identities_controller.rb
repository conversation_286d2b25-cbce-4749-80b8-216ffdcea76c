# frozen_string_literal: true

module Taskers
  # Tasker's photo identities controller.
  #
  class PhotoIdentitiesController < ApplicationController
    before_action :set_tasker, only: %i[destroy]

    def destroy
      authorize! :destroy, 'Tasker::PhotoIdentity'

      update_photo_identity

      respond_to do |format|
        format.turbo_stream { render_turbo_response }
        format.html { handle_html_response }
      end
    end

    private

    def set_tasker
      @tasker = Tasker.find params[:tasker_id]
    end

    def update_photo_identity
      @tasker.history.update(
        photo_identity_checked_at: nil,
        photo_identity_transaction_id: nil,
        photo_identity_transaction_created_at: nil,
        photo_identity_score: nil,
        photo_identity_score_received_at: nil
      )
    end

    def handle_html_response
      flash[:success] = t('tasker.photo_identity_removed')
      redirect_back(fallback_location: root_path)
    end

    def render_turbo_response
      flash.now[:success] = t('tasker.photo_identity_removed')

      render turbo_stream: [
        turbo_stream.replace("photo_identity_#{@tasker.id}",
                             partial: 'taskers/components/photo_identity',
                             locals: { tasker: @tasker.decorate }),
        turbo_stream.update('flash-messages', partial: 'shared/flashs')
      ]
    end
  end
end
